# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/
dist/

# Production
build

# Misc
.DS_Store
*.tsbuildinfo

# Debug
*.log

# Local env files
.env*.local
.env.development
.env.test

# Vercel
.vercel

# Typescript
*.tsbuildinfo

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README*.md
CHANGELOG.md
LICENSE

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Testing
coverage/
.nyc_output/
jest.config.js
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Linting
.eslintrc*
.prettierrc*
.stylelintrc*

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml
