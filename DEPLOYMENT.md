# 🚀 دليل النشر - نظام نتائج الثانوية العامة 2025

هذا الدليل يوضح كيفية نشر تطبيق نتائج الثانوية العامة المحسن بـ Bootstrap على منصات الاستضافة المختلفة.

## 📋 متطلبات ما قبل النشر

### ✅ التحقق من الجاهزية
```bash
# 1. تشغيل الاختبارات
npm run type-check
npm run lint
npm run build

# 2. اختبار البناء المحلي
npm run preview
```

### 🔧 إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env.local` وقم بتحديث القيم:

```bash
cp .env.example .env.local
```

## 🌐 النشر على Vercel (الموصى به)

### الطريقة الأولى: النشر التلقائي
1. **ربط المستودع بـ Vercel:**
   ```bash
   npm install -g vercel
   vercel login
   vercel --prod
   ```

2. **إعداد متغيرات البيئة في Vercel:**
   - اذهب إلى Vercel Dashboard
   - اختر مشروعك > Settings > Environment Variables
   - أضف المتغيرات من `.env.example`

3. **النشر:**
   ```bash
   vercel --prod
   ```

### الطريقة الثانية: GitHub Integration
1. **ربط GitHub بـ Vercel:**
   - اذهب إلى [vercel.com](https://vercel.com)
   - اختر "Import Git Repository"
   - اختر مستودع المشروع

2. **إعداد البناء:**
   - Build Command: `npm run build`
   - Output Directory: `.next`
   - Install Command: `npm ci`

## 🔷 النشر على Netlify

### الطريقة الأولى: Netlify CLI
```bash
# تثبيت Netlify CLI
npm install -g netlify-cli

# تسجيل الدخول
netlify login

# النشر
netlify deploy --prod --dir=.next
```

### الطريقة الثانية: Git Integration
1. **ربط المستودع:**
   - اذهب إلى [netlify.com](https://netlify.com)
   - اختر "New site from Git"
   - اختر مستودع المشروع

2. **إعدادات البناء:**
   - Build command: `npm run build`
   - Publish directory: `.next`
   - Node version: `18`

## 🐳 النشر باستخدام Docker

### بناء الصورة
```bash
# بناء صورة Docker
docker build -t thanaweya-portal .

# تشغيل الحاوية
docker run -p 3000:3000 thanaweya-portal
```

### النشر على خدمات السحابة
```bash
# AWS ECS
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com
docker tag thanaweya-portal:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/thanaweya-portal:latest
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/thanaweya-portal:latest

# Google Cloud Run
gcloud builds submit --tag gcr.io/PROJECT-ID/thanaweya-portal
gcloud run deploy --image gcr.io/PROJECT-ID/thanaweya-portal --platform managed
```

## ⚙️ إعدادات الإنتاج

### متغيرات البيئة المطلوبة
```env
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_API_BASE_URL=https://your-domain.com/api
COMPRESSION_ENABLED=true
IMAGE_OPTIMIZATION=true
```

### إعدادات الأمان
```env
CSP_ENABLED=true
RATE_LIMITING_ENABLED=true
CORS_ORIGINS=https://your-domain.com
```

## 🔍 مراقبة الأداء

### إعداد Analytics
```env
NEXT_PUBLIC_GA_TRACKING_ID=GA_MEASUREMENT_ID
VERCEL_ANALYTICS_ID=your_vercel_analytics_id
```

### مراقبة الأخطاء
```env
SENTRY_DSN=your_sentry_dsn
```

## 📊 تحسين الأداء

### 1. ضغط الملفات
- تم تفعيل ضغط gzip تلقائياً
- تحسين الصور باستخدام Next.js Image

### 2. التخزين المؤقت
- API responses: 5 دقائق
- Static assets: سنة واحدة
- Excel data: ساعة واحدة

### 3. CDN
- Vercel: تلقائي
- Netlify: تلقائي
- Custom: استخدم CloudFlare

## 🛡️ الأمان

### Headers الأمان
```
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: origin-when-cross-origin
Permissions-Policy: camera=(), microphone=(), geolocation=()
```

### Rate Limiting
- API calls: 100 طلب/دقيقة
- Search endpoint: محدود حسب IP

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في بناء TypeScript
```bash
npm run type-check
# إصلاح الأخطاء المعروضة
```

#### 2. مشاكل في تحميل Excel
- تأكد من وجود `results.xlsx` في مجلد `public`
- تحقق من صحة البيانات في الملف

#### 3. مشاكل في Bootstrap
```bash
# إعادة تثبيت Bootstrap
npm uninstall bootstrap react-bootstrap
npm install bootstrap@^5.3.7 react-bootstrap@^2.10.10
```

#### 4. مشاكل في الأداء
- استخدم `npm run build:analyze` لتحليل الحزم
- تحقق من حجم الملفات في `.next/static`

## 📈 مراقبة ما بعد النشر

### 1. فحص الصحة
```bash
curl -f https://your-domain.com/api/search?seating_no=1001660
```

### 2. مراقبة الأداء
- Core Web Vitals
- API response times
- Error rates

### 3. النسخ الاحتياطي
- نسخ احتياطي يومي لملف Excel
- مراقبة استخدام الموارد

## 🔄 التحديثات

### نشر التحديثات
```bash
# 1. اختبار التحديثات محلياً
npm run build
npm run preview

# 2. النشر
git push origin main  # للنشر التلقائي
# أو
vercel --prod  # للنشر اليدوي
```

### Rollback
```bash
# Vercel
vercel rollback [deployment-url]

# Netlify
netlify deploy --prod --dir=.next
```

---

## 📞 الدعم الفني

في حالة مواجهة مشاكل:
1. تحقق من logs المنصة
2. راجع متغيرات البيئة
3. تأكد من صحة ملف Excel
4. اختبر محلياً أولاً

**نصيحة:** احتفظ بنسخة احتياطية من الإعدادات والبيانات دائماً!
