# نظام نتائج الثانوية العامة 2025

نظام شامل للبحث عن نتائج الثانوية العامة المصرية 2025 مع إحصائيات تفصيلية وتقويم أكاديمي.

## المميزات

### 🔍 البحث السريع
- البحث برقم الجلوس
- البحث بالاسم
- عرض النتائج بتصميم احترافي

### ⚙️ البحث المتقدم
- فلترة النتائج حسب الدرجات
- فلترة حسب مستوى التقدير
- ترتيب النتائج بطرق مختلفة
- عرض مفصل للنتائج

### 📊 الإحصائيات العامة
- إجمالي عدد الطلاب
- المتوسط العام والدرجات العليا والدنيا
- توزيع الدرجات حسب التقديرات
- أوائل الطلاب

### 📅 التقويم الأكاديمي
- مواعيد الامتحانات
- الأحداث المهمة
- الإعلانات والمواعيد النهائية

## التقنيات المستخدمة

- **Next.js 15** - إطار عمل React للتطبيقات الحديثة
- **TypeScript** - للكتابة الآمنة للكود
- **Tailwind CSS** - للتصميم المتجاوب
- **XLSX** - لقراءة ملفات Excel
- **Arabic Font Support** - دعم الخطوط العربية

## البيانات

يستخدم النظام ملف Excel يحتوي على:
- **810,980 طالب** من نتائج الثانوية العامة 2025
- رقم الجلوس، الاسم، والدرجة الكلية من 320
- النظام الجديد للثانوية العامة

## التشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn

### التثبيت

```bash
# تثبيت المكتبات
npm install

# تشغيل الخادم المحلي
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

### ملف البيانات

تأكد من وجود ملف `results.xlsx` في مجلد `public/` يحتوي على:
- عمود `seating_no`: رقم الجلوس
- عمود `arabic_name`: اسم الطالب
- عمود `total_degree`: الدرجة الكلية

## الميزات التقنية

### الأداء
- تخزين مؤقت للبيانات
- تحسين استعلامات البحث
- تصميم متجاوب للجوال

### إمكانية الوصول
- دعم RTL للغة العربية
- تصميم متجاوب لجميع الأجهزة
- ألوان متباينة للوضوح

### الأمان
- قراءة البيانات من الخادم فقط
- عدم تعديل البيانات الأصلية
- حماية من الاستعلامات الضارة

## هيكل المشروع

```
src/
├── app/
│   ├── api/          # API routes
│   ├── globals.css   # الأنماط العامة
│   ├── layout.tsx    # التخطيط الأساسي
│   └── page.tsx      # الصفحة الرئيسية
├── components/       # المكونات
│   ├── Header.tsx
│   ├── SearchSection.tsx
│   ├── AdvancedSearch.tsx
│   ├── StatisticsSection.tsx
│   ├── AcademicCalendar.tsx
│   ├── ResultCard.tsx
│   └── MobileNav.tsx
└── lib/
    └── excel-reader.ts # قارئ ملفات Excel
```

## API Endpoints

### البحث
```
GET /api/search?seating_no=123456
GET /api/search?name=محمد
```

### الإحصائيات
```
GET /api/stats
```

## المساهمة

هذا المشروع مفتوح المصدر ومرحب بالمساهمات لتحسينه وإضافة ميزات جديدة.

## الترخيص

تم تطوير هذا النظام لخدمة طلاب الثانوية العامة في جمهورية مصر العربية.

---

**ملاحظة**: هذا النظام تعليمي وليس رسمياً من وزارة التربية والتعليم المصرية.
