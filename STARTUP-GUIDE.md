# Thanaweya Portal - Startup Guide
# دليل تشغيل بوابة الثانوية العامة

This guide explains how to use the automated startup scripts for the Thanaweya Portal application.

يشرح هذا الدليل كيفية استخدام سكريبتات التشغيل التلقائي لتطبيق بوابة الثانوية العامة.

## 📋 Prerequisites / المتطلبات المسبقة

### Required Software / البرامج المطلوبة
- **Node.js 18+** - Download from [nodejs.org](https://nodejs.org/)
- **npm** (comes with Node.js)
- **Windows 10/11** (for batch scripts)

### Required Files / الملفات المطلوبة
- `results.xlsx` - Must be placed in the `public/` folder
- يجب وضع ملف `results.xlsx` في مجلد `public/`

## 🚀 Startup Scripts / سكريبتات التشغيل

### 1. Development Mode / وضع التطوير

#### Option A: Batch Script (Recommended for beginners)
```bash
# Double-click or run from command prompt
start.bat
```

#### Option B: PowerShell Script (Advanced users)
```powershell
# Run from PowerShell
.\start.ps1

# With options
.\start.ps1 -Clean          # Clean build cache first
.\start.ps1 -Port 8080      # Use custom port
```

### 2. Production Mode / وضع الإنتاج

#### Production Batch Script
```bash
# For production deployment
start-production.bat
```

#### PowerShell Production Mode
```powershell
# Production mode with build
.\start.ps1 -Production -Build

# Production with clean build
.\start.ps1 -Production -Build -Clean
```

## 🔧 Script Features / ميزات السكريبتات

### Automated Checks / الفحوصات التلقائية
- ✅ Node.js installation and version verification
- ✅ npm availability check
- ✅ Project directory validation
- ✅ Dependencies installation/verification
- ✅ Data files presence check
- ✅ TypeScript compilation verification
- ✅ Environment variables setup

### Error Handling / معالجة الأخطاء
- 🛡️ Graceful error handling with clear messages
- 🛡️ Bilingual error messages (English/Arabic)
- 🛡️ User prompts for critical decisions
- 🛡️ Automatic fallback strategies

### Environment Setup / إعداد البيئة
- 🔧 Automatic `.env.local` creation
- 🔧 Development/Production environment configuration
- 🔧 Port configuration (default: 3000)
- 🔧 Security headers and optimizations

## 📁 Generated Files / الملفات المُنشأة

The scripts will automatically create:
ستقوم السكريبتات بإنشاء:

```
.env.local              # Development environment variables
.env.production.local   # Production environment variables (production script only)
node_modules/          # Dependencies (if not present)
.next/                 # Build output (after build)
```

## 🎯 Usage Examples / أمثلة الاستخدام

### Quick Start (Development)
```bash
# Just double-click start.bat
# Or from command prompt:
start.bat
```

### Advanced PowerShell Usage
```powershell
# Clean start with custom port
.\start.ps1 -Clean -Port 8080

# Production build and start
.\start.ps1 -Production -Build

# Development with clean cache
.\start.ps1 -Clean
```

### Production Deployment
```bash
# Full production setup
start-production.bat
```

## 🔍 Troubleshooting / استكشاف الأخطاء

### Common Issues / المشاكل الشائعة

#### 1. Node.js Not Found
**Problem:** `Node.js is not installed or not in PATH!`
**Solution:** 
- Install Node.js from [nodejs.org](https://nodejs.org/)
- Restart command prompt/PowerShell after installation
- Verify with `node --version`

#### 2. Permission Denied (PowerShell)
**Problem:** `Execution of scripts is disabled on this system`
**Solution:**
```powershell
# Run as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 3. Missing Data File
**Problem:** `results.xlsx not found in public folder!`
**Solution:**
- Ensure `results.xlsx` is in the `public/` directory
- Check file name spelling and extension
- Verify file is not corrupted

#### 4. Port Already in Use
**Problem:** `Port 3000 is already in use`
**Solution:**
```powershell
# Use custom port
.\start.ps1 -Port 8080
```

#### 5. Build Failures
**Problem:** TypeScript or build errors
**Solution:**
- Run `npm run type-check` to see specific errors
- Check for syntax errors in TypeScript files
- Ensure all dependencies are installed

### Getting Help / الحصول على المساعدة

If you encounter issues:
إذا واجهت مشاكل:

1. **Check the console output** - Error messages are displayed in both English and Arabic
2. **Verify prerequisites** - Ensure Node.js 18+ is installed
3. **Check file permissions** - Ensure you have read/write access to the project directory
4. **Review the logs** - Scripts provide detailed logging of each step

## 🌐 Network Access / الوصول عبر الشبكة

Once started, the application will be available at:
بمجرد التشغيل، سيكون التطبيق متاحاً على:

- **Local:** `http://localhost:3000`
- **Network:** `http://[your-ip-address]:3000`

To find your IP address:
للعثور على عنوان IP الخاص بك:

```bash
# Windows Command Prompt
ipconfig

# PowerShell
Get-NetIPAddress -AddressFamily IPv4
```

## 🔒 Security Considerations / اعتبارات الأمان

### Development Mode
- Runs on localhost only by default
- Debug information enabled
- Hot reloading enabled

### Production Mode
- Security headers enabled
- Compression enabled
- Console logs removed
- Rate limiting enabled
- CORS protection

## 📊 Performance Optimization / تحسين الأداء

The scripts automatically enable:
تقوم السكريبتات بتفعيل:

- **Compression** - Reduces file sizes
- **Image Optimization** - Optimizes images automatically
- **Caching** - Improves load times
- **Code Splitting** - Reduces initial bundle size

## 🔄 Updates and Maintenance / التحديثات والصيانة

### Updating Dependencies
```bash
# Update all dependencies
npm update

# Or use the clean flag
.\start.ps1 -Clean
```

### Clearing Cache
```bash
# Manual cache clearing
npm run clean

# Or use PowerShell with clean flag
.\start.ps1 -Clean
```

## 📞 Support / الدعم

For technical support or questions:
للدعم الفني أو الأسئلة:

- Check the main README.md file
- Review the DEPLOYMENT.md for advanced configurations
- Ensure all prerequisites are met
- Check the console output for specific error messages

---

**Note:** These scripts are designed for the Thanaweya Portal application and may need modifications for other Next.js projects.

**ملاحظة:** هذه السكريبتات مصممة لتطبيق بوابة الثانوية العامة وقد تحتاج إلى تعديلات للمشاريع الأخرى.
