@echo off
REM ========================================================================
REM Thanaweya Portal - Main Launcher
REM بوابة الثانوية العامة - المشغل الرئيسي
REM ========================================================================
REM This launcher provides options to start the application in different modes
REM يوفر هذا المشغل خيارات لبدء التطبيق في أوضاع مختلفة
REM ========================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Thanaweya Portal Launcher - مشغل بوابة الثانوية العامة
color 0B

:main_menu
cls
echo.
echo ========================================================================
echo                    Thanaweya Portal Launcher
echo                   مشغل بوابة الثانوية العامة
echo ========================================================================
echo.
echo Select startup mode / اختر وضع التشغيل:
echo.
echo [1] Development Mode (Recommended for testing)
echo     وضع التطوير (موصى به للاختبار)
echo     - Fast startup with hot reloading
echo     - بدء سريع مع إعادة التحميل التلقائي
echo.
echo [2] Production Mode (For deployment)
echo     وضع الإنتاج (للنشر)
echo     - Optimized build with full features
echo     - بناء محسن مع جميع الميزات
echo.
echo [3] PowerShell Advanced Mode
echo     وضع PowerShell المتقدم
echo     - Advanced options and better error handling
echo     - خيارات متقدمة ومعالجة أفضل للأخطاء
echo.
echo [4] Check System Requirements
echo     فحص متطلبات النظام
echo.
echo [5] View Documentation
echo     عرض الوثائق
echo.
echo [6] Exit / خروج
echo.
echo ========================================================================
echo.
set /p choice="Enter your choice (1-6) / أدخل اختيارك (1-6): "

if "%choice%"=="1" goto dev_mode
if "%choice%"=="2" goto prod_mode
if "%choice%"=="3" goto powershell_mode
if "%choice%"=="4" goto check_requirements
if "%choice%"=="5" goto view_docs
if "%choice%"=="6" goto exit_launcher

echo.
echo [ERROR] Invalid choice! Please select 1-6.
echo [خطأ] اختيار غير صحيح! يرجى اختيار 1-6.
pause
goto main_menu

:dev_mode
cls
echo.
echo ========================================================================
echo  Starting Development Mode
echo  بدء وضع التطوير
echo ========================================================================
echo.
echo [INFO] Launching development server...
echo [معلومات] تشغيل خادم التطوير...
echo.
if exist "start.bat" (
    call start.bat
) else (
    echo [ERROR] start.bat not found!
    echo [خطأ] لم يتم العثور على start.bat!
    pause
)
goto main_menu

:prod_mode
cls
echo.
echo ========================================================================
echo  Starting Production Mode
echo  بدء وضع الإنتاج
echo ========================================================================
echo.
echo [WARNING] This will build the application for production.
echo [تحذير] سيتم بناء التطبيق للإنتاج.
echo This process may take several minutes.
echo قد تستغرق هذه العملية عدة دقائق.
echo.
set /p confirm="Continue? (y/n) / المتابعة؟ (y/n): "
if /i "%confirm%" neq "y" goto main_menu

echo.
echo [INFO] Launching production server...
echo [معلومات] تشغيل خادم الإنتاج...
echo.
if exist "start-production.bat" (
    call start-production.bat
) else (
    echo [ERROR] start-production.bat not found!
    echo [خطأ] لم يتم العثور على start-production.bat!
    pause
)
goto main_menu

:powershell_mode
cls
echo.
echo ========================================================================
echo  PowerShell Advanced Mode
echo  وضع PowerShell المتقدم
echo ========================================================================
echo.
echo Available options / الخيارات المتاحة:
echo.
echo [1] Development with clean cache
echo     التطوير مع تنظيف التخزين المؤقت
echo.
echo [2] Production with build
echo     الإنتاج مع البناء
echo.
echo [3] Custom port (8080)
echo     منفذ مخصص (8080)
echo.
echo [4] Back to main menu
echo     العودة للقائمة الرئيسية
echo.
set /p ps_choice="Enter choice (1-4) / أدخل الاختيار (1-4): "

if "%ps_choice%"=="1" (
    echo [INFO] Starting PowerShell with clean cache...
    echo [معلومات] بدء PowerShell مع تنظيف التخزين المؤقت...
    powershell -ExecutionPolicy Bypass -File "start.ps1" -Clean
) else if "%ps_choice%"=="2" (
    echo [INFO] Starting PowerShell in production mode...
    echo [معلومات] بدء PowerShell في وضع الإنتاج...
    powershell -ExecutionPolicy Bypass -File "start.ps1" -Production -Build
) else if "%ps_choice%"=="3" (
    echo [INFO] Starting PowerShell with custom port 8080...
    echo [معلومات] بدء PowerShell مع المنفذ المخصص 8080...
    powershell -ExecutionPolicy Bypass -File "start.ps1" -Port 8080
) else if "%ps_choice%"=="4" (
    goto main_menu
) else (
    echo [ERROR] Invalid choice!
    echo [خطأ] اختيار غير صحيح!
    pause
)
goto main_menu

:check_requirements
cls
echo.
echo ========================================================================
echo  System Requirements Check
echo  فحص متطلبات النظام
echo ========================================================================
echo.

REM Check Node.js
echo [INFO] Checking Node.js...
echo [معلومات] فحص Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [❌] Node.js: NOT INSTALLED
    echo [❌] Node.js: غير مثبت
    echo     Download from: https://nodejs.org/
    echo     تحميل من: https://nodejs.org/
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VER=%%i
    echo [✅] Node.js: !NODE_VER!
)

REM Check npm
echo [INFO] Checking npm...
echo [معلومات] فحص npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [❌] npm: NOT INSTALLED
    echo [❌] npm: غير مثبت
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VER=%%i
    echo [✅] npm: !NPM_VER!
)

REM Check PowerShell
echo [INFO] Checking PowerShell...
echo [معلومات] فحص PowerShell...
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo [❌] PowerShell: NOT AVAILABLE
    echo [❌] PowerShell: غير متاح
) else (
    echo [✅] PowerShell: Available
    echo [✅] PowerShell: متاح
)

REM Check project files
echo [INFO] Checking project files...
echo [معلومات] فحص ملفات المشروع...
if exist "package.json" (
    echo [✅] package.json: Found
    echo [✅] package.json: موجود
) else (
    echo [❌] package.json: NOT FOUND
    echo [❌] package.json: غير موجود
)

if exist "public\results.xlsx" (
    echo [✅] results.xlsx: Found
    echo [✅] results.xlsx: موجود
) else (
    echo [⚠️] results.xlsx: NOT FOUND (Optional)
    echo [⚠️] results.xlsx: غير موجود (اختياري)
)

if exist "node_modules" (
    echo [✅] node_modules: Found
    echo [✅] node_modules: موجود
) else (
    echo [⚠️] node_modules: NOT FOUND (Will be installed)
    echo [⚠️] node_modules: غير موجود (سيتم تثبيته)
)

echo.
echo ========================================================================
echo.
pause
goto main_menu

:view_docs
cls
echo.
echo ========================================================================
echo  Documentation / الوثائق
echo ========================================================================
echo.
echo Available documentation files:
echo ملفات الوثائق المتاحة:
echo.

if exist "README.md" (
    echo [📖] README.md - Main project documentation
    echo     الوثائق الرئيسية للمشروع
)

if exist "STARTUP-GUIDE.md" (
    echo [🚀] STARTUP-GUIDE.md - Startup scripts guide
    echo     دليل سكريبتات التشغيل
)

if exist "DEPLOYMENT.md" (
    echo [🌐] DEPLOYMENT.md - Deployment guide
    echo     دليل النشر
)

if exist "PRODUCTION-READY.md" (
    echo [⚙️] PRODUCTION-READY.md - Production setup
    echo     إعداد الإنتاج
)

echo.
echo [INFO] You can open these files with any text editor.
echo [معلومات] يمكنك فتح هذه الملفات بأي محرر نصوص.
echo.
echo Would you like to open the startup guide?
echo هل تريد فتح دليل التشغيل؟
set /p open_guide="Open STARTUP-GUIDE.md? (y/n) / فتح STARTUP-GUIDE.md؟ (y/n): "

if /i "%open_guide%"=="y" (
    if exist "STARTUP-GUIDE.md" (
        start notepad "STARTUP-GUIDE.md"
    ) else (
        echo [ERROR] STARTUP-GUIDE.md not found!
        echo [خطأ] لم يتم العثور على STARTUP-GUIDE.md!
    )
)

echo.
pause
goto main_menu

:exit_launcher
cls
echo.
echo ========================================================================
echo  Thank you for using Thanaweya Portal!
echo  شكراً لاستخدام بوابة الثانوية العامة!
echo ========================================================================
echo.
echo Visit us at: https://github.com/your-repo
echo زورونا على: https://github.com/your-repo
echo.
echo Press any key to exit...
echo اضغط أي مفتاح للخروج...
pause >nul
exit /b 0
