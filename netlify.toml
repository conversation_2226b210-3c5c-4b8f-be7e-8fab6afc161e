[build]
  command = "npm run build"
  publish = ".next"
  
[build.environment]
  NODE_ENV = "production"
  NEXT_TELEMETRY_DISABLED = "1"

# Production context
[context.production]
  command = "npm run build"
  
[context.production.environment]
  NODE_ENV = "production"
  NEXT_PUBLIC_APP_URL = "https://your-domain.netlify.app"
  NEXT_PUBLIC_API_BASE_URL = "https://your-domain.netlify.app/api"

# Deploy preview context
[context.deploy-preview]
  command = "npm run build"
  
[context.deploy-preview.environment]
  NODE_ENV = "production"

# Branch deploy context
[context.branch-deploy]
  command = "npm run build"

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
    X-XSS-Protection = "1; mode=block"

[[headers]]
  for = "/api/*"
  [headers.values]
    Cache-Control = "public, max-age=300, s-maxage=300"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.jpeg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.gif"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.ico"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.svg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.woff"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Redirects
[[redirects]]
  from = "/results"
  to = "/"
  status = 301

[[redirects]]
  from = "/search"
  to = "/"
  status = 301

# SPA fallback for client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Functions
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Edge functions
[edge_functions]
  directory = "netlify/edge-functions"
