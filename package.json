{"name": "thanaweya-portal", "version": "2.0.0", "description": "نظام شامل للبحث عن نتائج الثانوية العامة المصرية 2025 مع Bootstrap", "private": true, "author": {"name": "وزارة التربية والتعليم", "email": "<EMAIL>"}, "homepage": "https://your-domain.com", "repository": {"type": "git", "url": "https://github.com/your-username/thanaweya-portal.git"}, "bugs": {"url": "https://github.com/your-username/thanaweya-portal/issues"}, "keywords": ["نتائج الثانوية العامة", "الثانوية العامة 2025", "نتائج الثانوية المصرية", "Bootstrap", "Next.js", "TypeScript", "React", "وزارة التربية والتعليم"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true next build", "export": "next export", "clean": "rmdir /s /q .next 2>nul & rmdir /s /q out 2>nul & rmdir /s /q dist 2>nul & echo Clean completed", "preview": "npm run build && npm run start"}, "dependencies": {"@popperjs/core": "^2.11.8", "@types/multer": "^2.0.0", "bootstrap": "^5.3.7", "multer": "^2.0.2", "next": "15.4.3", "react": "19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "19.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bootstrap": "^5.2.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "typescript": "^5"}}