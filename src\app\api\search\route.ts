import { NextRequest, NextResponse } from 'next/server';
import { ServerExcelReader, StudentResult } from '@/lib/excel-reader';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const seatingNo = searchParams.get('seating_no');
    const name = searchParams.get('name');

    if (!seatingNo && !name) {
      return NextResponse.json(
        { error: 'يجب إدخال رقم الجلوس أو الاسم للبحث' },
        { status: 400 }
      );
    }

    const data = await ServerExcelReader.readExcelFile();

    let results: StudentResult[] = [];

    if (seatingNo) {
      const seatingNumber = parseInt(seatingNo);
      if (isNaN(seatingNumber)) {
        return NextResponse.json(
          { error: 'رقم الجلوس يجب أن يكون رقماً صحيحاً' },
          { status: 400 }
        );
      }

      const student = data.find(s => s.seating_no === seatingNumber);
      if (student) {
        results = [student];
      }
    } else if (name) {
      const searchTerm = name.toLowerCase().trim();
      results = data.filter(student => 
        student.arabic_name.toLowerCase().includes(searchTerm)
      ).slice(0, 50); // Limit results to 50 for performance
    }

    return NextResponse.json({
      success: true,
      results,
      count: results.length
    });

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء البحث' },
      { status: 500 }
    );
  }
}
