import { NextResponse } from 'next/server';
import { ServerExcelReader } from '@/lib/excel-reader';

export async function GET() {
  try {
    const data = await ServerExcelReader.readExcelFile();

    if (data.length === 0) {
      return NextResponse.json(
        { error: 'لا توجد بيانات متاحة' },
        { status: 404 }
      );
    }

    const degrees = data.map(s => s.total_degree);
    const total = degrees.reduce((sum, degree) => sum + degree, 0);
    const average = total / degrees.length;

    // Use reduce to find max and min to avoid stack overflow with large arrays
    const max = degrees.length > 0 ? degrees.reduce((maxVal, degree) => degree > maxVal ? degree : maxVal, degrees[0]!) : 0;
    const min = degrees.length > 0 ? degrees.reduce((minVal, degree) => degree < minVal ? degree : minVal, degrees[0]!) : 0;

    // Grade distribution based on Egyptian grading system (percentage of 320)
    const gradeRanges = {
      excellent: degrees.filter(d => (d / 320) * 100 >= 85).length, // امتياز
      veryGood: degrees.filter(d => (d / 320) * 100 >= 75 && (d / 320) * 100 < 85).length, // جيد جداً
      good: degrees.filter(d => (d / 320) * 100 >= 65 && (d / 320) * 100 < 75).length, // جيد
      acceptable: degrees.filter(d => (d / 320) * 100 >= 50 && (d / 320) * 100 < 65).length, // مقبول
      weak: degrees.filter(d => (d / 320) * 100 < 50).length // ضعيف
    };

    // Top 10 students
    const topStudents = [...data]
      .sort((a, b) => b.total_degree - a.total_degree)
      .slice(0, 10);

    const statistics = {
      totalStudents: data.length,
      average: Math.round(average * 100) / 100,
      highest: max,
      lowest: min,
      gradeDistribution: gradeRanges,
      topStudents
    };

    return NextResponse.json({
      success: true,
      statistics
    });

  } catch (error) {
    console.error('Statistics API error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب الإحصائيات' },
      { status: 500 }
    );
  }
}
