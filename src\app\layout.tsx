import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import BootstrapProvider from "@/components/BootstrapProvider";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "نتائج الثانوية العامة 2025 - النظام الجديد",
    template: "%s | نتائج الثانوية العامة 2025"
  },
  description: "نظام شامل للبحث عن نتائج الثانوية العامة المصرية 2025 مع إحصائيات تفصيلية وتقويم أكاديمي. ابحث برقم الجلوس أو الاسم واحصل على النتائج فوراً.",
  keywords: [
    "نتائج الثانوية العامة",
    "الثانوية العامة 2025",
    "نتائج الثانوية المصرية",
    "البحث برقم الجلوس",
    "إحصائيات الثانوية العامة",
    "وزارة التربية والتعليم",
    "نتائج الامتحانات"
  ],
  authors: [{ name: "وزارة التربية والتعليم - جمهورية مصر العربية" }],
  creator: "نظام نتائج الثانوية العامة",
  publisher: "وزارة التربية والتعليم",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://your-domain.com'),
  alternates: {
    canonical: '/',
    languages: {
      'ar-EG': '/',
    },
  },
  openGraph: {
    title: "نتائج الثانوية العامة 2025 - النظام الجديد",
    description: "نظام شامل للبحث عن نتائج الثانوية العامة المصرية 2025",
    url: 'https://your-domain.com',
    siteName: 'نتائج الثانوية العامة 2025',
    locale: 'ar_EG',
    type: 'website',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'نتائج الثانوية العامة 2025',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "نتائج الثانوية العامة 2025",
    description: "البحث عن نتائج الثانوية العامة المصرية 2025",
    images: ['/twitter-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://cdn.jsdelivr.net" />

        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://cdn.jsdelivr.net" />

        {/* Optimized font loading */}
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />

        {/* Viewport and mobile optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
        <meta name="theme-color" content="#667eea" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />

        {/* Performance hints */}
        <link rel="preload" href="/results.xlsx" as="fetch" crossOrigin="anonymous" />

        {/* Theme initialization script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  const savedTheme = localStorage.getItem('theme');
                  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                  const theme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

                  if (theme === 'dark') {
                    document.documentElement.classList.add('dark');
                    document.body.setAttribute('data-bs-theme', 'dark');
                  } else {
                    document.body.setAttribute('data-bs-theme', 'light');
                  }
                } catch (e) {
                  console.warn('Theme initialization failed:', e);
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        style={{ fontFamily: "'Noto Sans Arabic', Arial, Helvetica, sans-serif" }}
      >
        <BootstrapProvider>
          {children}
        </BootstrapProvider>
      </body>
    </html>
  );
}
