'use client';

import { useState } from 'react';

interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  type: 'exam' | 'holiday' | 'announcement' | 'deadline';
  description?: string;
}

export default function AcademicCalendar() {
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  // Sample academic events for Thanaweya Amma
  const events: CalendarEvent[] = [
    {
      id: '1',
      title: 'بداية امتحانات الثانوية العامة',
      date: '2025-06-01',
      type: 'exam',
      description: 'بداية امتحانات الدور الأول للثانوية العامة 2025'
    },
    {
      id: '2',
      title: 'امتحان اللغة العربية',
      date: '2025-06-03',
      type: 'exam',
      description: 'امتحان اللغة العربية للثانوية العامة'
    },
    {
      id: '3',
      title: 'امتحان اللغة الإنجليزية',
      date: '2025-06-05',
      type: 'exam',
      description: 'امتحان اللغة الإنجليزية للثانوية العامة'
    },
    {
      id: '4',
      title: 'امتحان الرياضيات',
      date: '2025-06-08',
      type: 'exam',
      description: 'امتحان الرياضيات للشعبة العلمية'
    },
    {
      id: '5',
      title: 'امتحان الفيزياء',
      date: '2025-06-10',
      type: 'exam',
      description: 'امتحان الفيزياء للشعبة العلمية'
    },
    {
      id: '6',
      title: 'امتحان الكيمياء',
      date: '2025-06-12',
      type: 'exam',
      description: 'امتحان الكيمياء للشعبة العلمية'
    },
    {
      id: '7',
      title: 'امتحان الأحياء',
      date: '2025-06-15',
      type: 'exam',
      description: 'امتحان الأحياء للشعبة العلمية'
    },
    {
      id: '8',
      title: 'نهاية امتحانات الثانوية العامة',
      date: '2025-06-20',
      type: 'exam',
      description: 'انتهاء امتحانات الدور الأول'
    },
    {
      id: '9',
      title: 'إعلان النتائج',
      date: '2025-07-15',
      type: 'announcement',
      description: 'إعلان نتائج الثانوية العامة 2025'
    },
    {
      id: '10',
      title: 'بداية تنسيق الجامعات',
      date: '2025-07-20',
      type: 'deadline',
      description: 'بداية المرحلة الأولى لتنسيق الجامعات'
    }
  ];

  const getEventTypeColor = (type: CalendarEvent['type']) => {
    switch (type) {
      case 'exam':
        return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 border-red-200 dark:border-red-700';
      case 'holiday':
        return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 border-green-200 dark:border-green-700';
      case 'announcement':
        return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-700';
      case 'deadline':
        return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 border-orange-200 dark:border-orange-700';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border-gray-200 dark:border-gray-600';
    }
  };

  const getEventTypeIcon = (type: CalendarEvent['type']) => {
    switch (type) {
      case 'exam':
        return '📝';
      case 'holiday':
        return '🎉';
      case 'announcement':
        return '📢';
      case 'deadline':
        return '⏰';
      default:
        return '📅';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const upcomingEvents = events
    .filter(event => new Date(event.date) >= new Date())
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5);

  const pastEvents = events
    .filter(event => new Date(event.date) < new Date())
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
          التقويم الأكاديمي
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          مواعيد امتحانات الثانوية العامة والأحداث المهمة
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-red-100 dark:bg-red-900 p-3 rounded-lg">
              <span className="text-2xl">📝</span>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">امتحانات قادمة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {events.filter(e => e.type === 'exam' && new Date(e.date) >= new Date()).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg">
              <span className="text-2xl">📢</span>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">إعلانات مهمة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {events.filter(e => e.type === 'announcement').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-orange-100 dark:bg-orange-900 p-3 rounded-lg">
              <span className="text-2xl">⏰</span>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">مواعيد نهائية</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {events.filter(e => e.type === 'deadline' && new Date(e.date) >= new Date()).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6">الأحداث القادمة</h3>
        <div className="space-y-4">
          {upcomingEvents.map((event) => (
            <div
              key={event.id}
              className={`p-4 rounded-lg border-2 ${getEventTypeColor(event.type)} hover:shadow-md transition-shadow cursor-pointer`}
              onClick={() => setSelectedDate(selectedDate === event.id ? null : event.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <span className="text-2xl ml-3">{getEventTypeIcon(event.type)}</span>
                  <div>
                    <h4 className="font-semibold text-lg mb-1">{event.title}</h4>
                    <p className="text-sm opacity-75">{formatDate(event.date)}</p>
                    {selectedDate === event.id && event.description && (
                      <p className="mt-2 text-sm">{event.description}</p>
                    )}
                  </div>
                </div>
                <div className="text-sm font-medium">
                  {Math.ceil((new Date(event.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} يوم
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Past Events */}
      {pastEvents.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6">الأحداث السابقة</h3>
          <div className="space-y-4">
            {pastEvents.map((event) => (
              <div
                key={event.id}
                className="p-4 rounded-lg border border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 opacity-75"
              >
                <div className="flex items-start">
                  <span className="text-xl ml-3 opacity-50">{getEventTypeIcon(event.type)}</span>
                  <div>
                    <h4 className="font-semibold text-gray-600 dark:text-gray-300 mb-1">{event.title}</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{formatDate(event.date)}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Important Notes */}
      <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6">
        <div className="flex items-start">
          <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400 ml-3 mt-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div>
            <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">ملاحظات مهمة</h4>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• يرجى مراجعة جدول الامتحانات بانتظام للتأكد من أي تحديثات</li>
              <li>• يجب الحضور قبل موعد الامتحان بـ 30 دقيقة على الأقل</li>
              <li>• إحضار بطاقة الرقم القومي وكارنيه المدرسة إلزامي</li>
              <li>• ممنوع إحضار الهواتف المحمولة أو أي أجهزة إلكترونية</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
