'use client';

import { useState } from 'react';
import ResultCard from './ResultCard';

interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

interface SearchFilters {
  minDegree: number;
  maxDegree: number;
  gradeLevel: string;
  sortBy: 'name' | 'degree' | 'seating_no';
  sortOrder: 'asc' | 'desc';
}

interface SearchResponse {
  success: boolean;
  results: StudentResult[];
  count: number;
  error?: string;
}

export default function AdvancedSearch() {
  const [searchValue, setSearchValue] = useState('');
  const [results, setResults] = useState<StudentResult[]>([]);
  const [filteredResults, setFilteredResults] = useState<StudentResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState<SearchFilters>({
    minDegree: 0,
    maxDegree: 320,
    gradeLevel: 'all',
    sortBy: 'degree',
    sortOrder: 'desc'
  });

  const handleSearch = async () => {
    if (!searchValue.trim()) {
      setError('يرجى إدخال اسم للبحث');
      return;
    }

    setLoading(true);
    setError('');
    setResults([]);
    setFilteredResults([]);

    try {
      const params = new URLSearchParams();
      params.append('name', searchValue.trim());

      const response = await fetch(`/api/search?${params}`);
      const data: SearchResponse = await response.json();

      if (data.success) {
        setResults(data.results);
        applyFilters(data.results);
        if (data.results.length === 0) {
          setError('لم يتم العثور على نتائج');
        }
      } else {
        setError(data.error || 'حدث خطأ أثناء البحث');
      }
    } catch {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = (data: StudentResult[] = results) => {
    let filtered = [...data];

    // Filter by degree range
    filtered = filtered.filter(
      student => student.total_degree >= filters.minDegree && student.total_degree <= filters.maxDegree
    );

    // Filter by grade level (based on percentage of 320)
    if (filters.gradeLevel !== 'all') {
      filtered = filtered.filter(student => {
        const percentage = (student.total_degree / 320) * 100;
        switch (filters.gradeLevel) {
          case 'excellent': return percentage >= 85;
          case 'veryGood': return percentage >= 75 && percentage < 85;
          case 'good': return percentage >= 65 && percentage < 75;
          case 'acceptable': return percentage >= 50 && percentage < 65;
          case 'weak': return percentage < 50;
          default: return true;
        }
      });
    }

    // Sort results
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (filters.sortBy) {
        case 'name':
          comparison = a.arabic_name.localeCompare(b.arabic_name, 'ar');
          break;
        case 'degree':
          comparison = a.total_degree - b.total_degree;
          break;
        case 'seating_no':
          comparison = a.seating_no - b.seating_no;
          break;
      }
      return filters.sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredResults(filtered);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: string | number) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    applyFilters();
  };

  const clearFilters = () => {
    const defaultFilters: SearchFilters = {
      minDegree: 0,
      maxDegree: 320,
      gradeLevel: 'all',
      sortBy: 'degree',
      sortOrder: 'desc'
    };
    setFilters(defaultFilters);
    applyFilters();
  };

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
          البحث المتقدم
        </h2>

        {/* Search Input */}
        <div className="max-w-md mx-auto mb-4">
          <div className="relative">
            <input
              type="text"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              placeholder="أدخل اسم الطالب للبحث..."
              className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-right"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        {/* Search and Filter Buttons */}
        <div className="flex justify-center space-x-4 rtl:space-x-reverse">
          <button
            onClick={handleSearch}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                جاري البحث...
              </>
            ) : (
              'بحث'
            )}
          </button>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center"
          >
            <svg className="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
            </svg>
            فلترة النتائج
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-4 p-4 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg text-red-700 dark:text-red-300 text-center">
            {error}
          </div>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && results.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold text-gray-800 dark:text-white">فلترة النتائج</h3>
            <button
              onClick={clearFilters}
              className="text-blue-500 hover:text-blue-600 text-sm font-medium"
            >
              مسح الفلاتر
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Degree Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                نطاق الدرجات
              </label>
              <div className="space-y-2">
                <input
                  type="number"
                  placeholder="الحد الأدنى"
                  value={filters.minDegree}
                  onChange={(e) => handleFilterChange('minDegree', parseInt(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-right"
                />
                <input
                  type="number"
                  placeholder="الحد الأقصى"
                  value={filters.maxDegree}
                  onChange={(e) => handleFilterChange('maxDegree', parseInt(e.target.value) || 320)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-right"
                />
              </div>
            </div>

            {/* Grade Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                مستوى التقدير
              </label>
              <select
                value={filters.gradeLevel}
                onChange={(e) => handleFilterChange('gradeLevel', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="all">جميع المستويات</option>
                <option value="excellent">امتياز (85%+)</option>
                <option value="veryGood">جيد جداً (75%-84%)</option>
                <option value="good">جيد (65%-74%)</option>
                <option value="acceptable">مقبول (50%-64%)</option>
                <option value="weak">ضعيف (&lt;50%)</option>
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                ترتيب حسب
              </label>
              <select
                value={filters.sortBy}
                onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="degree">الدرجة</option>
                <option value="name">الاسم</option>
                <option value="seating_no">رقم الجلوس</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                نوع الترتيب
              </label>
              <select
                value={filters.sortOrder}
                onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="desc">تنازلي</option>
                <option value="asc">تصاعدي</option>
              </select>
            </div>
          </div>

          {/* Results Count */}
          <div className="mt-4 text-center text-gray-600 dark:text-gray-300">
            عرض {filteredResults.length} من أصل {results.length} نتيجة
          </div>
        </div>
      )}

      {/* Search Results */}
      {filteredResults.length > 0 && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
              نتائج البحث المفلترة
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              عرض {filteredResults.length} من أصل {results.length} نتيجة
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredResults.map((student) => (
              <ResultCard
                key={student.seating_no}
                student={student}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
