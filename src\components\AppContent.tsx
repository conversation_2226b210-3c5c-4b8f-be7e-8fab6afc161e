'use client';

import { useState } from 'react';
import SearchSection from '@/components/SearchSection';
import StatisticsSection from '@/components/StatisticsSection';
import AdvancedSearch from '@/components/AdvancedSearch';
import AcademicCalendar from '@/components/AcademicCalendar';
import Header from '@/components/Header';
import MobileNav from '@/components/MobileNav';
import ThemeToggle from '@/components/ThemeToggle';

export default function AppContent() {
  const [activeTab, setActiveTab] = useState<'search' | 'advanced' | 'stats' | 'calendar'>('search');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 dark:text-white mb-4">
            نتائج الثانوية العامة 2025
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            البحث عن النتائج والإحصائيات - النظام الجديد
          </p>
        </div>

        {/* Navigation */}
        <MobileNav activeTab={activeTab} onTabChange={setActiveTab} />

        {/* Content Sections */}
        <div className="max-w-6xl mx-auto">
          {activeTab === 'search' && <SearchSection />}
          {activeTab === 'advanced' && <AdvancedSearch />}
          {activeTab === 'stats' && <StatisticsSection />}
          {activeTab === 'calendar' && <AcademicCalendar />}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center text-gray-600 dark:text-gray-400">
            <p>© 2025 نظام نتائج الثانوية العامة - جمهورية مصر العربية</p>
            <p className="text-sm mt-2">تم تطويره لخدمة طلاب الثانوية العامة</p>
          </div>
        </div>
      </footer>

      {/* Mobile Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
