'use client';

import { useState } from 'react';
import <PERSON>trapHeader from './BootstrapHeader';
import BootstrapMobileNav from './BootstrapMobileNav';
import BootstrapSearchSection from './BootstrapSearchSection';
import BootstrapStatisticsSection from './BootstrapStatisticsSection';
import AdvancedSearch from './AdvancedSearch';
import AcademicCalendar from './AcademicCalendar';
import ThemeToggle from './ThemeToggle';

export default function BootstrapAppContent() {
  const [activeTab, setActiveTab] = useState<'search' | 'advanced' | 'stats' | 'calendar'>('search');

  return (
    <div className="min-vh-100 bg-light">
      <BootstrapHeader />
      
      <main className="py-4">
        {/* Welcome Section */}
        <div className="container mb-4">
          <div className="row">
            <div className="col-12">
              <div className="jumbotron bg-gradient-primary text-white text-center p-3 p-md-5 rounded shadow-sm fade-in">
                <div className="container">
                  <h1 className="display-6 display-md-4 fw-bold mb-3">
                    نتائج الثانوية العامة 2025
                  </h1>
                  <p className="lead mb-4 fs-6 fs-md-5">
                    البحث عن النتائج والإحصائيات - النظام الجديد
                  </p>
                  <div className="d-flex justify-content-center gap-2 gap-md-3 flex-wrap">
                    <span className="badge bg-light text-dark fs-6 px-2 px-md-3 py-2">
                      <svg className="me-1 d-none d-sm-inline" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                      </svg>
                      <span className="d-none d-sm-inline">810,980 طالب</span>
                      <span className="d-sm-none">810K طالب</span>
                    </span>
                    <span className="badge bg-light text-dark fs-6 px-2 px-md-3 py-2">
                      <svg className="me-1 d-none d-sm-inline" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      <span className="d-none d-sm-inline">العام الدراسي 2025</span>
                      <span className="d-sm-none">2025</span>
                    </span>
                    <span className="badge bg-light text-dark fs-6 px-2 px-md-3 py-2">
                      <svg className="me-1 d-none d-sm-inline" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="d-none d-sm-inline">نتائج معتمدة</span>
                      <span className="d-sm-none">معتمدة</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <BootstrapMobileNav activeTab={activeTab} onTabChange={setActiveTab} />

        {/* Content Sections */}
        <div className="container">
          <div className="row">
            <div className="col-12">
              {activeTab === 'search' && <BootstrapSearchSection />}
              {activeTab === 'advanced' && (
                <div className="card shadow-sm">
                  <div className="card-header bg-info text-white">
                    <h3 className="card-title mb-0">البحث المتقدم</h3>
                  </div>
                  <div className="card-body">
                    <AdvancedSearch />
                  </div>
                </div>
              )}
              {activeTab === 'stats' && <BootstrapStatisticsSection />}
              {activeTab === 'calendar' && (
                <div className="card shadow-sm">
                  <div className="card-header bg-success text-white">
                    <h3 className="card-title mb-0">التقويم الأكاديمي</h3>
                  </div>
                  <div className="card-body">
                    <AcademicCalendar />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-dark text-light py-4 mt-5">
        <div className="container">
          <div className="row">
            <div className="col-md-6">
              <h5 className="fw-bold mb-3">نظام نتائج الثانوية العامة</h5>
              <p className="mb-2">
                <svg className="me-2" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2v8h12V6H4z" clipRule="evenodd" />
                </svg>
                وزارة التربية والتعليم
              </p>
              <p className="mb-2">
                <svg className="me-2" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                جمهورية مصر العربية
              </p>
            </div>
            <div className="col-md-6">
              <h5 className="fw-bold mb-3">معلومات مهمة</h5>
              <ul className="list-unstyled">
                <li className="mb-2">
                  <svg className="me-2" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  النتائج معتمدة رسمياً
                </li>
                <li className="mb-2">
                  <svg className="me-2" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
                  </svg>
                  خدمة مجانية للطلاب
                </li>
                <li className="mb-2">
                  <svg className="me-2" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  بيانات محمية وآمنة
                </li>
              </ul>
            </div>
          </div>
          <hr className="my-4" />
          <div className="row">
            <div className="col-12 text-center">
              <p className="mb-0">
                © 2025 نظام نتائج الثانوية العامة - جمهورية مصر العربية
              </p>
              <small className="text-muted">
                تم تطويره لخدمة طلاب الثانوية العامة
              </small>
            </div>
          </div>
        </div>
      </footer>

      {/* Mobile Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
