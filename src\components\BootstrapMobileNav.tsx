'use client';

interface BootstrapMobileNavProps {
  activeTab: 'search' | 'advanced' | 'stats' | 'calendar';
  onTabChange: (tab: 'search' | 'advanced' | 'stats' | 'calendar') => void;
}

export default function BootstrapMobileNav({ activeTab, onTabChange }: BootstrapMobileNavProps) {
  const tabs = [
    {
      id: 'search' as const,
      label: 'البحث السريع',
      icon: (
        <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'advanced' as const,
      label: 'البحث المتقدم',
      icon: (
        <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
        </svg>
      )
    },
    {
      id: 'stats' as const,
      label: 'الإحصائيات',
      icon: (
        <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
        </svg>
      )
    },
    {
      id: 'calendar' as const,
      label: 'التقويم الأكاديمي',
      icon: (
        <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
        </svg>
      )
    }
  ];

  return (
    <div className="container-fluid mb-4">
      {/* Desktop Navigation */}
      <div className="d-none d-md-block">
        <ul className="nav nav-pills nav-fill justify-content-center">
          {tabs.map((tab) => (
            <li key={tab.id} className="nav-item">
              <button
                className={`nav-link d-flex align-items-center ${
                  activeTab === tab.id ? 'active' : ''
                }`}
                onClick={() => onTabChange(tab.id)}
              >
                {tab.icon}
                {tab.label}
              </button>
            </li>
          ))}
        </ul>
      </div>

      {/* Mobile Navigation */}
      <div className="d-md-none">
        <div className="btn-group w-100" role="group">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              type="button"
              className={`btn ${
                activeTab === tab.id ? 'btn-primary' : 'btn-outline-primary'
              } d-flex flex-column align-items-center py-2`}
              onClick={() => onTabChange(tab.id)}
            >
              {tab.icon}
              <small className="mt-1">{tab.label}</small>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
