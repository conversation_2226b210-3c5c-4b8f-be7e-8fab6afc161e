'use client';

interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

interface BootstrapResultCardProps {
  student: StudentResult;
}

export default function BootstrapResultCard({ student }: BootstrapResultCardProps) {
  const percentage = (student.total_degree / 320) * 100;
  
  // Determine grade level and styling
  const getGradeInfo = (percentage: number) => {
    if (percentage >= 85) {
      return { 
        grade: 'امتياز', 
        bgClass: 'bg-success', 
        textClass: 'text-success',
        badgeClass: 'bg-success'
      };
    } else if (percentage >= 75) {
      return { 
        grade: 'جيد جداً', 
        bgClass: 'bg-primary', 
        textClass: 'text-primary',
        badgeClass: 'bg-primary'
      };
    } else if (percentage >= 65) {
      return { 
        grade: 'جيد', 
        bgClass: 'bg-warning', 
        textClass: 'text-warning',
        badgeClass: 'bg-warning'
      };
    } else if (percentage >= 50) {
      return { 
        grade: 'مقبول', 
        bgClass: 'bg-info', 
        textClass: 'text-info',
        badgeClass: 'bg-info'
      };
    } else {
      return { 
        grade: 'ضعيف', 
        bgClass: 'bg-danger', 
        textClass: 'text-danger',
        badgeClass: 'bg-danger'
      };
    }
  };

  const gradeInfo = getGradeInfo(percentage);
  const isPass = percentage >= 50;

  return (
    <div className="card shadow-sm border-0">
      <div className={`card-header ${gradeInfo.bgClass} text-white`}>
        <div className="d-flex justify-content-between align-items-center">
          <h4 className="card-title mb-0">
            <svg className="me-2" width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
            </svg>
            نتيجة الطالب
          </h4>
          <span className={`badge ${gradeInfo.badgeClass} fs-6`}>
            {gradeInfo.grade}
          </span>
        </div>
      </div>
      
      <div className="card-body">
        {/* Student Info */}
        <div className="row mb-4">
          <div className="col-md-6">
            <div className="d-flex align-items-center mb-3">
              <svg className="me-2 text-muted" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              <div>
                <small className="text-muted">اسم الطالب</small>
                <div className="fw-bold">{student.arabic_name}</div>
              </div>
            </div>
          </div>
          <div className="col-md-6">
            <div className="d-flex align-items-center mb-3">
              <svg className="me-2 text-muted" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z" clipRule="evenodd" />
              </svg>
              <div>
                <small className="text-muted">رقم الجلوس</small>
                <div className="fw-bold" dir="ltr">{student.seating_no}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Grade Details */}
        <div className="row mb-4 g-3">
          <div className="col-md-4 col-sm-6">
            <div className="text-center p-3 bg-light rounded h-100 slide-in">
              <div className={`display-6 fw-bold ${gradeInfo.textClass} fs-2 fs-md-1`}>
                {student.total_degree}
              </div>
              <small className="text-muted">من 320</small>
            </div>
          </div>
          <div className="col-md-4 col-sm-6">
            <div className="text-center p-3 bg-light rounded h-100 slide-in">
              <div className={`display-6 fw-bold ${gradeInfo.textClass} fs-2 fs-md-1`}>
                {percentage.toFixed(1)}%
              </div>
              <small className="text-muted">النسبة المئوية</small>
            </div>
          </div>
          <div className="col-md-4 col-12">
            <div className="text-center p-3 bg-light rounded h-100 slide-in">
              <div className={`h4 fw-bold ${gradeInfo.textClass} fs-3 fs-md-2`}>
                {gradeInfo.grade}
              </div>
              <small className="text-muted">التقدير</small>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="d-flex justify-content-between mb-2">
            <span className="text-muted">التقدم</span>
            <span className="fw-bold">{percentage.toFixed(1)}%</span>
          </div>
          <div className="progress" style={{ height: '10px' }}>
            <div 
              className={`progress-bar ${gradeInfo.bgClass}`}
              role="progressbar" 
              style={{ width: `${Math.min(percentage, 100)}%` }}
              aria-valuenow={percentage} 
              aria-valuemin={0} 
              aria-valuemax={100}
            ></div>
          </div>
        </div>

        {/* Success/Failure Message */}
        {isPass ? (
          <div className="alert alert-success d-flex align-items-center">
            <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <div>
              <strong>مبروك!</strong> لقد نجحت في امتحان الثانوية العامة
            </div>
          </div>
        ) : (
          <div className="alert alert-danger d-flex align-items-center">
            <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <div>
              للأسف، لم تحقق الدرجة المطلوبة للنجاح
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="d-grid gap-2 d-md-flex mt-4">
          <button
            className="btn btn-outline-primary flex-md-fill"
            onClick={() => window.print()}
          >
            <svg className="me-2 d-none d-sm-inline" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clipRule="evenodd" />
            </svg>
            <span className="d-none d-sm-inline">طباعة النتيجة</span>
            <span className="d-sm-none">طباعة</span>
          </button>
          <button
            className="btn btn-outline-success flex-md-fill"
            onClick={() => {
              const text = `نتيجة الطالب: ${student.arabic_name}\nرقم الجلوس: ${student.seating_no}\nالدرجة: ${student.total_degree}/320 (${percentage.toFixed(1)}%)\nالتقدير: ${gradeInfo.grade}`;
              navigator.clipboard.writeText(text);
            }}
          >
            <svg className="me-2 d-none d-sm-inline" width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
              <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
            </svg>
            <span className="d-none d-sm-inline">نسخ النتيجة</span>
            <span className="d-sm-none">نسخ</span>
          </button>
        </div>
      </div>
    </div>
  );
}
