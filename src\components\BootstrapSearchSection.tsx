'use client';

import { useState } from 'react';
import BootstrapResultCard from './BootstrapResultCard';
import LoadingSpinner from './LoadingSpinner';
import ToastNotification from './ToastNotification';

interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

export default function BootstrapSearchSection() {
  const [seatingNo, setSeatingNo] = useState('');
  const [results, setResults] = useState<StudentResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'warning' | 'info'>('info');

  const showToastMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
    setToastMessage(message);
    setToastType(type);
    setShowToast(true);
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!seatingNo.trim()) {
      setError('يرجى إدخال رقم الجلوس');
      showToastMessage('يرجى إدخال رقم الجلوس', 'warning');
      return;
    }

    setLoading(true);
    setError('');
    setResults([]);

    try {
      const response = await fetch(`/api/search?seating_no=${encodeURIComponent(seatingNo.trim())}`);
      const data = await response.json();

      if (data.success) {
        if (data.results && data.results.length > 0) {
          setResults(data.results);
          showToastMessage('تم العثور على النتيجة بنجاح!', 'success');
        } else {
          setError('لم يتم العثور على نتائج');
          showToastMessage('لم يتم العثور على نتائج لهذا الرقم', 'info');
        }
      } else {
        setError(data.error || 'حدث خطأ أثناء البحث');
        showToastMessage('حدث خطأ أثناء البحث', 'error');
      }
    } catch {
      setError('حدث خطأ في الاتصال');
      showToastMessage('حدث خطأ في الاتصال', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <ToastNotification
        message={toastMessage}
        type={toastType}
        show={showToast}
        onClose={() => setShowToast(false)}
      />

      <div className="row justify-content-center">
        <div className="col-lg-8">
          {/* Search Card */}
          <div className="card shadow-sm mb-4">
            <div className="card-header bg-primary text-white">
              <h3 className="card-title mb-0 d-flex align-items-center">
                <svg className="me-2" width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
                البحث عن النتائج
              </h3>
            </div>
            <div className="card-body">
              <form onSubmit={handleSearch}>
                <div className="mb-3">
                  <label htmlFor="seatingNo" className="form-label fw-bold">
                    رقم الجلوس
                  </label>
                  <div className="input-group">
                    <input
                      type="text"
                      id="seatingNo"
                      className="form-control form-control-lg text-center"
                      placeholder="أدخل رقم الجلوس..."
                      value={seatingNo}
                      onChange={(e) => setSeatingNo(e.target.value)}
                      disabled={loading}
                      dir="ltr"
                    />
                    <button
                      type="submit"
                      className="btn btn-primary btn-lg"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          جاري البحث...
                        </>
                      ) : (
                        <>
                          <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                          </svg>
                          بحث
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>

              {/* Instructions */}
              <div className="alert alert-info d-flex align-items-center">
                <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <strong>تعليمات البحث:</strong>
                  <ul className="mb-0 mt-2">
                    <li>أدخل رقم الجلوس كاملاً</li>
                    <li>تأكد من صحة الرقم المدخل</li>
                    <li>النتائج تظهر فوراً بعد البحث</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="card shadow-sm mb-4">
              <div className="card-body">
                <LoadingSpinner message="جاري البحث عن النتيجة..." size="md" variant="primary" />
              </div>
            </div>
          )}

          {/* Error Alert */}
          {error && !loading && (
            <div className="alert alert-danger d-flex align-items-center shake" role="alert">
              <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {error}
            </div>
          )}

          {/* Results */}
          {results.length > 0 && !loading && (
            <div className="row">
              {results.map((student, index) => (
                <div key={student.seating_no} className="col-12 mb-4">
                  <div className="bounce-in" style={{ animationDelay: `${index * 0.1}s` }}>
                    <BootstrapResultCard student={student} />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
