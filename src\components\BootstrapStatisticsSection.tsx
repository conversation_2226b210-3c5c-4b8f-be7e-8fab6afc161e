'use client';

import { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface Statistics {
  totalStudents: number;
  average: number;
  highest: number;
  lowest: number;
  gradeDistribution: {
    excellent: number;
    veryGood: number;
    good: number;
    acceptable: number;
    weak: number;
  };
  topStudents: Array<{
    seating_no: number;
    arabic_name: string;
    total_degree: number;
  }>;
}

export default function BootstrapStatisticsSection() {
  const [stats, setStats] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      const response = await fetch('/api/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.statistics);
      } else {
        setError(data.error || 'حدث خطأ أثناء جلب الإحصائيات');
      }
    } catch {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="card shadow-sm">
          <div className="card-body" style={{ minHeight: '400px' }}>
            <LoadingSpinner
              message="جاري تحميل الإحصائيات..."
              size="lg"
              variant="primary"
            />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container">
        <div className="alert alert-danger d-flex align-items-center" role="alert">
          <svg className="me-2" width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </div>
      </div>
    );
  }

  if (!stats) return null;

  const gradeData = [
    { label: 'امتياز (85%+)', count: stats.gradeDistribution.excellent, color: 'success', percentage: (stats.gradeDistribution.excellent / stats.totalStudents * 100).toFixed(1) },
    { label: 'جيد جداً (75%-84%)', count: stats.gradeDistribution.veryGood, color: 'primary', percentage: (stats.gradeDistribution.veryGood / stats.totalStudents * 100).toFixed(1) },
    { label: 'جيد (65%-74%)', count: stats.gradeDistribution.good, color: 'warning', percentage: (stats.gradeDistribution.good / stats.totalStudents * 100).toFixed(1) },
    { label: 'مقبول (50%-64%)', count: stats.gradeDistribution.acceptable, color: 'info', percentage: (stats.gradeDistribution.acceptable / stats.totalStudents * 100).toFixed(1) },
    { label: 'ضعيف (<50%)', count: stats.gradeDistribution.weak, color: 'danger', percentage: (stats.gradeDistribution.weak / stats.totalStudents * 100).toFixed(1) },
  ];

  return (
    <div className="container">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card bg-primary text-white">
            <div className="card-body text-center">
              <h2 className="card-title mb-0">
                <svg className="me-2" width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                </svg>
                إحصائيات الثانوية العامة 2025
              </h2>
              <p className="mb-0">تحليل شامل لنتائج جميع الطلاب</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Statistics Cards */}
      <div className="row mb-4 g-3">
        <div className="col-lg-3 col-md-6">
          <div className="card bg-info text-white h-100 fade-in">
            <div className="card-body text-center p-3 p-md-4">
              <svg className="mb-2 d-none d-md-block" width="48" height="48" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
              </svg>
              <svg className="mb-2 d-md-none" width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
              </svg>
              <h3 className="display-6 fw-bold fs-3 fs-md-2">{stats.totalStudents.toLocaleString()}</h3>
              <p className="mb-0 small">إجمالي الطلاب</p>
            </div>
          </div>
        </div>
        <div className="col-lg-3 col-md-6">
          <div className="card bg-success text-white h-100 fade-in">
            <div className="card-body text-center p-3 p-md-4">
              <svg className="mb-2 d-none d-md-block" width="48" height="48" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <svg className="mb-2 d-md-none" width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <h3 className="display-6 fw-bold fs-3 fs-md-2">{stats.highest}</h3>
              <p className="mb-0 small">أعلى درجة</p>
            </div>
          </div>
        </div>
        <div className="col-lg-3 col-md-6">
          <div className="card bg-warning text-white h-100 fade-in">
            <div className="card-body text-center p-3 p-md-4">
              <svg className="mb-2 d-none d-md-block" width="48" height="48" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <svg className="mb-2 d-md-none" width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <h3 className="display-6 fw-bold fs-3 fs-md-2">{stats.average.toFixed(1)}</h3>
              <p className="mb-0 small">المتوسط العام</p>
            </div>
          </div>
        </div>
        <div className="col-lg-3 col-md-6">
          <div className="card bg-danger text-white h-100 fade-in">
            <div className="card-body text-center p-3 p-md-4">
              <svg className="mb-2 d-none d-md-block" width="48" height="48" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <svg className="mb-2 d-md-none" width="32" height="32" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <h3 className="display-6 fw-bold fs-3 fs-md-2">{stats.lowest}</h3>
              <p className="mb-0 small">أقل درجة</p>
            </div>
          </div>
        </div>
      </div>

      {/* Grade Distribution */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h4 className="card-title mb-0">توزيع التقديرات</h4>
            </div>
            <div className="card-body">
              <div className="row">
                {gradeData.map((grade, index) => (
                  <div key={index} className="col-lg-2 col-md-4 col-sm-6 mb-3">
                    <div className={`card bg-${grade.color} text-white h-100`}>
                      <div className="card-body text-center p-3">
                        <h5 className="fw-bold">{grade.count.toLocaleString()}</h5>
                        <small>{grade.label}</small>
                        <div className="mt-2">
                          <span className="badge bg-light text-dark">{grade.percentage}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Students */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header bg-warning text-dark">
              <h4 className="card-title mb-0 d-flex align-items-center">
                <svg className="me-2" width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                أوائل الطلاب
              </h4>
            </div>
            <div className="card-body">
              <div className="row">
                {stats.topStudents.slice(0, 10).map((student, index) => (
                  <div key={student.seating_no} className="col-lg-6 mb-3">
                    <div className="card border-0 bg-light">
                      <div className="card-body d-flex align-items-center">
                        <div className={`badge ${index < 3 ? 'bg-warning' : 'bg-secondary'} me-3 fs-6`}>
                          #{index + 1}
                        </div>
                        <div className="flex-grow-1">
                          <h6 className="mb-1 fw-bold">{student.arabic_name}</h6>
                          <small className="text-muted">رقم الجلوس: {student.seating_no}</small>
                        </div>
                        <div className="text-end">
                          <div className="fw-bold text-success">{student.total_degree}</div>
                          <small className="text-muted">من 320</small>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
