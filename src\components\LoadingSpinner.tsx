'use client';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
}

export default function LoadingSpinner({ 
  message = 'جاري التحميل...', 
  size = 'md',
  variant = 'primary' 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'spinner-border-sm',
    md: '',
    lg: 'spinner-border-lg'
  };

  const textSizeClasses = {
    sm: 'fs-6',
    md: 'fs-5',
    lg: 'fs-4'
  };

  return (
    <div className="d-flex flex-column align-items-center justify-content-center p-4 fade-in">
      <div className={`spinner-border text-${variant} ${sizeClasses[size]} mb-3`} role="status">
        <span className="visually-hidden">{message}</span>
      </div>
      <p className={`text-muted ${textSizeClasses[size]} mb-0 text-center`}>
        {message}
      </p>
    </div>
  );
}
