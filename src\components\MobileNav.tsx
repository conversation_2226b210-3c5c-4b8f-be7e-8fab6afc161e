'use client';

import { useState } from 'react';

interface MobileNavProps {
  activeTab: 'search' | 'advanced' | 'stats' | 'calendar';
  onTabChange: (tab: 'search' | 'advanced' | 'stats' | 'calendar') => void;
}

export default function MobileNav({ activeTab, onTabChange }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false);

  const tabs = [
    { id: 'search', label: 'البحث السريع', icon: '🔍' },
    { id: 'advanced', label: 'البحث المتقدم', icon: '⚙️' },
    { id: 'stats', label: 'الإحصائيات', icon: '📊' },
    { id: 'calendar', label: 'التقويم', icon: '📅' }
  ] as const;

  const handleTabChange = (tabId: 'search' | 'advanced' | 'stats' | 'calendar') => {
    onTabChange(tabId);
    setIsOpen(false);
  };

  return (
    <>
      {/* Desktop Navigation - Hidden on mobile */}
      <div className="hidden md:flex justify-center mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-lg">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`px-4 py-3 rounded-md font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <span className="ml-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden mb-6">
        {/* Mobile Tab Selector */}
        <div className="relative">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="w-full bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex items-center justify-between"
          >
            <div className="flex items-center">
              <span className="text-xl ml-3">
                {tabs.find(tab => tab.id === activeTab)?.icon}
              </span>
              <span className="font-medium text-gray-800 dark:text-white">
                {tabs.find(tab => tab.id === activeTab)?.label}
              </span>
            </div>
            <svg
              className={`w-5 h-5 text-gray-600 dark:text-gray-300 transform transition-transform ${
                isOpen ? 'rotate-180' : ''
              }`}
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>

          {/* Dropdown Menu */}
          {isOpen && (
            <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-50">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`w-full px-4 py-3 text-right flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                      : 'text-gray-700 dark:text-gray-300'
                  } ${tab.id === tabs[0]?.id ? 'rounded-t-lg' : ''} ${
                    tab.id === tabs[tabs.length - 1]?.id ? 'rounded-b-lg' : ''
                  }`}
                >
                  <span className="text-xl ml-3">{tab.icon}</span>
                  <span className="font-medium">{tab.label}</span>
                  {activeTab === tab.id && (
                    <svg className="w-5 h-5 mr-auto text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Mobile Bottom Navigation - Alternative approach */}
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-600 z-40 md:hidden">
          <div className="grid grid-cols-4">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`p-4 text-center transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <div className="text-xl mb-1">{tab.icon}</div>
                <div className="text-xs font-medium">{tab.label}</div>
                {activeTab === tab.id && (
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-blue-500 rounded-b-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Overlay for mobile dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Spacer for bottom navigation */}
      <div className="h-20 md:hidden"></div>
    </>
  );
}
