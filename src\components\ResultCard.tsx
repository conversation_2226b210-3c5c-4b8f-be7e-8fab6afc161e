'use client';

interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

interface ResultCardProps {
  student: StudentResult;
  rank?: number;
  showRank?: boolean;
}

export default function ResultCard({ student, rank, showRank = false }: ResultCardProps) {
  const getGradeInfo = (degree: number) => {
    // Based on percentage for 320 total marks
    const percentage = (degree / 320) * 100;

    if (percentage >= 85) {
      return {
        label: 'امتياز',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-100 dark:bg-green-900',
        borderColor: 'border-green-200 dark:border-green-700',
        icon: '🏆'
      };
    }
    if (percentage >= 75) {
      return {
        label: 'جيد جداً',
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-100 dark:bg-blue-900',
        borderColor: 'border-blue-200 dark:border-blue-700',
        icon: '⭐'
      };
    }
    if (percentage >= 65) {
      return {
        label: 'جيد',
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-100 dark:bg-yellow-900',
        borderColor: 'border-yellow-200 dark:border-yellow-700',
        icon: '👍'
      };
    }
    if (percentage >= 50) {
      return {
        label: 'مقبول',
        color: 'text-orange-600 dark:text-orange-400',
        bgColor: 'bg-orange-100 dark:bg-orange-900',
        borderColor: 'border-orange-200 dark:border-orange-700',
        icon: '✓'
      };
    }
    return {
      label: 'ضعيف',
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-100 dark:bg-red-900',
      borderColor: 'border-red-200 dark:border-red-700',
      icon: '⚠️'
    };
  };

  const getPercentage = (degree: number) => {
    // Total marks for Egyptian Thanaweya Amma is 320
    const totalMarks = 320;
    return ((degree / totalMarks) * 100).toFixed(1);
  };

  const gradeInfo = getGradeInfo(student.total_degree);
  const percentage = getPercentage(student.total_degree);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border-2 ${gradeInfo.borderColor} p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1`}>
      {/* Header with rank if applicable */}
      {showRank && rank && (
        <div className="flex justify-between items-center mb-4">
          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${gradeInfo.bgColor} ${gradeInfo.color}`}>
            <span className="ml-1">{gradeInfo.icon}</span>
            المرتبة #{rank}
          </div>
        </div>
      )}

      {/* Student Info */}
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
          {student.arabic_name}
        </h3>
        <div className="flex items-center text-gray-600 dark:text-gray-300">
          <svg className="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
          <span>رقم الجلوس: {student.seating_no}</span>
        </div>
      </div>

      {/* Grade Display */}
      <div className="text-center mb-6">
        <div className="relative">
          {/* Circular Progress */}
          <div className="w-32 h-32 mx-auto mb-4">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
              {/* Background circle */}
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="currentColor"
                strokeWidth="8"
                className="text-gray-200 dark:text-gray-700"
              />
              {/* Progress circle */}
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="currentColor"
                strokeWidth="8"
                strokeLinecap="round"
                className={gradeInfo.color}
                strokeDasharray={`${(parseFloat(percentage) / 100) * 314} 314`}
              />
            </svg>
            {/* Center content */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-3xl font-bold text-gray-800 dark:text-white">
                {student.total_degree}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                {percentage}%
              </div>
            </div>
          </div>
        </div>

        {/* Grade Label */}
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${gradeInfo.bgColor} ${gradeInfo.color}`}>
          <span className="ml-2 text-xl">{gradeInfo.icon}</span>
          {gradeInfo.label}
        </div>
      </div>

      {/* Additional Info */}
      <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="text-center">
            <div className="text-gray-600 dark:text-gray-400">النسبة المئوية</div>
            <div className="font-semibold text-gray-800 dark:text-white">{percentage}%</div>
          </div>
          <div className="text-center">
            <div className="text-gray-600 dark:text-gray-400">من أصل</div>
            <div className="font-semibold text-gray-800 dark:text-white">320 درجة</div>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {((student.total_degree / 320) * 100) >= 50 && (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-green-600 dark:text-green-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-green-700 dark:text-green-300 font-medium">
              مبروك! نجح الطالب في امتحان الثانوية العامة
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
