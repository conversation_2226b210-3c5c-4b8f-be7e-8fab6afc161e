'use client';

import { useState } from 'react';
import ResultCard from './ResultCard';

interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

interface SearchResponse {
  success: boolean;
  results: StudentResult[];
  count: number;
  error?: string;
}

export default function SearchSection() {
  const [searchType, setSearchType] = useState<'seating_no' | 'name'>('seating_no');
  const [searchValue, setSearchValue] = useState('');
  const [results, setResults] = useState<StudentResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSearch = async () => {
    if (!searchValue.trim()) {
      setError('يرجى إدخال قيمة للبحث');
      return;
    }

    setLoading(true);
    setError('');
    setResults([]);

    try {
      const params = new URLSearchParams();
      if (searchType === 'seating_no') {
        params.append('seating_no', searchValue.trim());
      } else {
        params.append('name', searchValue.trim());
      }

      const response = await fetch(`/api/search?${params}`);
      const data: SearchResponse = await response.json();

      if (data.success) {
        setResults(data.results);
        if (data.results.length === 0) {
          setError('لم يتم العثور على نتائج');
        }
      } else {
        setError(data.error || 'حدث خطأ أثناء البحث');
      }
    } catch {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="space-y-6">
      {/* Search Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 text-center">
          البحث عن النتائج
        </h2>

        {/* Search Type Selection */}
        <div className="flex justify-center mb-6">
          <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setSearchType('seating_no')}
              className={`px-4 py-2 rounded-md font-medium transition-all ${
                searchType === 'seating_no'
                  ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              البحث برقم الجلوس
            </button>
            <button
              onClick={() => setSearchType('name')}
              className={`px-4 py-2 rounded-md font-medium transition-all ${
                searchType === 'name'
                  ? 'bg-white dark:bg-gray-600 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-300'
              }`}
            >
              البحث بالاسم
            </button>
          </div>
        </div>

        {/* Search Input */}
        <div className="max-w-md mx-auto">
          <div className="relative">
            <input
              type={searchType === 'seating_no' ? 'number' : 'text'}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              placeholder={
                searchType === 'seating_no' 
                  ? 'أدخل رقم الجلوس...' 
                  : 'أدخل اسم الطالب...'
              }
              className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-right"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          
          <button
            onClick={handleSearch}
            disabled={loading}
            className="w-full mt-4 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                جاري البحث...
              </>
            ) : (
              'بحث'
            )}
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mt-4 p-4 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg text-red-700 dark:text-red-300 text-center">
            {error}
          </div>
        )}
      </div>

      {/* Search Results */}
      {results.length > 0 && (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
              نتائج البحث
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              تم العثور على {results.length} {results.length === 1 ? 'نتيجة' : 'نتيجة'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {results.map((student) => (
              <ResultCard
                key={student.seating_no}
                student={student}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
