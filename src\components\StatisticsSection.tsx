'use client';

import { useState, useEffect } from 'react';
import ResultCard from './ResultCard';

interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

interface Statistics {
  totalStudents: number;
  average: number;
  highest: number;
  lowest: number;
  gradeDistribution: {
    excellent: number;
    veryGood: number;
    good: number;
    acceptable: number;
    weak: number;
  };
  topStudents: StudentResult[];
}

interface StatsResponse {
  success: boolean;
  statistics: Statistics;
  error?: string;
}

export default function StatisticsSection() {
  const [stats, setStats] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/stats');
      const data: StatsResponse = await response.json();

      if (data.success) {
        setStats(data.statistics);
      } else {
        setError(data.error || 'حدث خطأ أثناء جلب الإحصائيات');
      }
    } catch {
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-lg p-6 text-center">
        <p className="text-red-700 dark:text-red-300">{error}</p>
        <button
          onClick={fetchStatistics}
          className="mt-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  if (!stats) return null;

  const gradeData = [
    { label: 'امتياز (85%+)', count: stats.gradeDistribution.excellent, color: 'bg-green-500', percentage: (stats.gradeDistribution.excellent / stats.totalStudents * 100).toFixed(1) },
    { label: 'جيد جداً (75%-84%)', count: stats.gradeDistribution.veryGood, color: 'bg-blue-500', percentage: (stats.gradeDistribution.veryGood / stats.totalStudents * 100).toFixed(1) },
    { label: 'جيد (65%-74%)', count: stats.gradeDistribution.good, color: 'bg-yellow-500', percentage: (stats.gradeDistribution.good / stats.totalStudents * 100).toFixed(1) },
    { label: 'مقبول (50%-64%)', count: stats.gradeDistribution.acceptable, color: 'bg-orange-500', percentage: (stats.gradeDistribution.acceptable / stats.totalStudents * 100).toFixed(1) },
    { label: 'ضعيف (<50%)', count: stats.gradeDistribution.weak, color: 'bg-red-500', percentage: (stats.gradeDistribution.weak / stats.totalStudents * 100).toFixed(1) },
  ];

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلاب</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {stats.totalStudents.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-green-100 dark:bg-green-900 p-3 rounded-lg">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">أعلى درجة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{stats.highest}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-lg">
              <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">المتوسط العام</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{stats.average}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center">
            <div className="bg-red-100 dark:bg-red-900 p-3 rounded-lg">
              <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">أقل درجة</p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">{stats.lowest}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Grade Distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-6">توزيع الدرجات</h3>
        <div className="space-y-4">
          {gradeData.map((grade, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center flex-1">
                <div className={`w-4 h-4 rounded ${grade.color} mr-3`}></div>
                <span className="text-gray-700 dark:text-gray-300 font-medium">{grade.label}</span>
              </div>
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <span className="text-gray-600 dark:text-gray-400 text-sm">
                  {grade.percentage}%
                </span>
                <span className="font-bold text-gray-800 dark:text-white min-w-[80px] text-right">
                  {grade.count.toLocaleString()}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Students */}
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">أوائل الطلاب</h3>
          <p className="text-gray-600 dark:text-gray-300">أفضل 10 طلاب في الثانوية العامة</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {stats.topStudents.map((student, index) => (
            <ResultCard
              key={student.seating_no}
              student={student}
              rank={index + 1}
              showRank={true}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
