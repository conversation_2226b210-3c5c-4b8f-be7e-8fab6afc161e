'use client';

import { useState, useEffect } from 'react';

interface ToastNotificationProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  show: boolean;
  onClose: () => void;
  duration?: number;
}

export default function ToastNotification({ 
  message, 
  type, 
  show, 
  onClose, 
  duration = 3000 
}: ToastNotificationProps) {
  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    setIsVisible(show);

    if (show && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onClose, 300); // Wait for animation to complete
      }, duration);

      return () => clearTimeout(timer);
    }

    return undefined;
  }, [show, duration, onClose]);

  if (!show && !isVisible) return null;

  const getToastClasses = () => {
    const baseClasses = 'toast show position-fixed top-0 end-0 m-3';
    const animationClass = isVisible ? 'slide-in' : 'fade-out';
    return `${baseClasses} ${animationClass}`;
  };

  const getIconAndColor = () => {
    switch (type) {
      case 'success':
        return {
          icon: (
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          ),
          bgClass: 'bg-success',
          textClass: 'text-white'
        };
      case 'error':
        return {
          icon: (
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          ),
          bgClass: 'bg-danger',
          textClass: 'text-white'
        };
      case 'warning':
        return {
          icon: (
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          ),
          bgClass: 'bg-warning',
          textClass: 'text-dark'
        };
      case 'info':
        return {
          icon: (
            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          ),
          bgClass: 'bg-info',
          textClass: 'text-white'
        };
      default:
        return {
          icon: null,
          bgClass: 'bg-light',
          textClass: 'text-dark'
        };
    }
  };

  const { icon, bgClass, textClass } = getIconAndColor();

  return (
    <div className={getToastClasses()} role="alert" style={{ zIndex: 1050 }}>
      <div className={`toast-header ${bgClass} ${textClass} border-0`}>
        <div className="d-flex align-items-center">
          {icon && <span className="me-2">{icon}</span>}
          <strong className="me-auto">إشعار</strong>
          <button
            type="button"
            className={`btn-close ${textClass === 'text-white' ? 'btn-close-white' : ''}`}
            onClick={() => {
              setIsVisible(false);
              setTimeout(onClose, 300);
            }}
            aria-label="إغلاق"
          ></button>
        </div>
      </div>
      <div className="toast-body bg-white text-dark">
        {message}
      </div>
    </div>
  );
}
