// Environment configuration and constants

export const config = {
  // Application settings
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'نظام نتائج الثانوية العامة 2025',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    description: process.env.NEXT_PUBLIC_SITE_DESCRIPTION || 'نظام شامل للبحث عن نتائج الثانوية العامة المصرية 2025',
  },

  // API configuration
  api: {
    baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
    rateLimit: parseInt(process.env.API_RATE_LIMIT || '100'),
    cacheTTL: parseInt(process.env.API_CACHE_TTL || '300'),
    timeout: 30000, // 30 seconds
  },

  // Excel file settings
  excel: {
    filePath: process.env.EXCEL_FILE_PATH || '/public/results.xlsx',
    cacheDuration: parseInt(process.env.EXCEL_CACHE_DURATION || '3600'),
    maxResults: parseInt(process.env.MAX_SEARCH_RESULTS || '100'),
  },

  // Performance settings
  performance: {
    enableAnalytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    enableServiceWorker: process.env.NEXT_PUBLIC_ENABLE_SERVICE_WORKER === 'true',
    compressionEnabled: process.env.COMPRESSION_ENABLED === 'true',
    imageOptimization: process.env.IMAGE_OPTIMIZATION === 'true',
    lazyLoadingThreshold: 0.1,
    debounceDelay: 300,
    cacheSize: 100,
  },

  // Security settings
  security: {
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    cspEnabled: process.env.CSP_ENABLED === 'true',
    rateLimitingEnabled: process.env.RATE_LIMITING_ENABLED === 'true',
  },

  // Analytics and monitoring
  analytics: {
    gaTrackingId: process.env.NEXT_PUBLIC_GA_TRACKING_ID,
    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
    sentryDsn: process.env.SENTRY_DSN,
    vercelAnalyticsId: process.env.VERCEL_ANALYTICS_ID,
  },

  // Social media and SEO
  social: {
    siteName: process.env.NEXT_PUBLIC_SITE_NAME || 'نتائج الثانوية العامة 2025',
    twitterHandle: process.env.NEXT_PUBLIC_TWITTER_HANDLE,
    facebookPage: process.env.NEXT_PUBLIC_FACEBOOK_PAGE,
  },

  // Development settings
  development: {
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    analyze: process.env.ANALYZE === 'true',
    bundleAnalyze: process.env.BUNDLE_ANALYZE === 'true',
  },

  // Deployment settings
  deployment: {
    platform: process.env.DEPLOYMENT_PLATFORM || 'vercel',
    vercelUrl: process.env.VERCEL_URL,
    netlifyUrl: process.env.NETLIFY_URL,
  },

  // UI constants
  ui: {
    maxGrade: 320,
    gradeThresholds: {
      excellent: 85, // 85% and above
      veryGood: 75,  // 75% - 84%
      good: 65,      // 65% - 74%
      acceptable: 50, // 50% - 64%
      // Below 50% is considered weak
    },
    animationDurations: {
      fast: 200,
      normal: 300,
      slow: 500,
    },
    breakpoints: {
      xs: 0,
      sm: 576,
      md: 768,
      lg: 992,
      xl: 1200,
      xxl: 1400,
    },
  },

  // Feature flags
  features: {
    advancedSearch: true,
    statistics: true,
    calendar: true,
    darkMode: true,
    printResults: true,
    exportResults: true,
    shareResults: false, // Disabled for privacy
    notifications: true,
    offlineMode: false, // Future feature
  },

  // Error messages
  errors: {
    networkError: 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.',
    notFound: 'لم يتم العثور على نتائج لهذا الرقم.',
    invalidInput: 'يرجى إدخال رقم جلوس صحيح.',
    serverError: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً.',
    rateLimitExceeded: 'تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً.',
  },

  // Success messages
  success: {
    resultFound: 'تم العثور على النتيجة بنجاح!',
    dataCopied: 'تم نسخ البيانات بنجاح!',
    printReady: 'النتيجة جاهزة للطباعة!',
  },
} as const;

// Type definitions for better TypeScript support
export type Config = typeof config;
export type GradeLevel = keyof typeof config.ui.gradeThresholds;
export type AnimationDuration = keyof typeof config.ui.animationDurations;
export type Breakpoint = keyof typeof config.ui.breakpoints;

// Helper functions
export const getGradeLevel = (percentage: number): GradeLevel => {
  if (percentage >= config.ui.gradeThresholds.excellent) return 'excellent';
  if (percentage >= config.ui.gradeThresholds.veryGood) return 'veryGood';
  if (percentage >= config.ui.gradeThresholds.good) return 'good';
  if (percentage >= config.ui.gradeThresholds.acceptable) return 'acceptable';
  return 'acceptable'; // This will be handled as 'weak' in the UI
};

export const isFeatureEnabled = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

export const getApiUrl = (endpoint: string): string => {
  return `${config.api.baseUrl}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
};

export const isDevelopment = (): boolean => config.development.isDevelopment;
export const isProduction = (): boolean => config.development.isProduction;
