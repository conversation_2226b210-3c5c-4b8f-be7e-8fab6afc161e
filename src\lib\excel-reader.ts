import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';

export interface StudentResult {
  seating_no: number;
  arabic_name: string;
  total_degree: number;
}

export class ExcelReader {
  private static instance: ExcelReader;
  private data: StudentResult[] = [];
  private isLoaded = false;

  private constructor() {}

  public static getInstance(): ExcelReader {
    if (!ExcelReader.instance) {
      ExcelReader.instance = new ExcelReader();
    }
    return ExcelReader.instance;
  }

  public async loadData(): Promise<void> {
    if (this.isLoaded) return;

    try {
      // In production, you might want to load from a different path
      const response = await fetch('/results.xlsx');
      const arrayBuffer = await response.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      
      // Get the first sheet (النظام الجديد)
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error('No sheets found in Excel file');
      }
      const worksheet = workbook.Sheets[sheetName];
      if (!worksheet) {
        throw new Error(`Sheet "${sheetName}" not found`);
      }
      
      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet) as StudentResult[];
      
      this.data = jsonData;
      this.isLoaded = true;
      
      console.log(`Loaded ${this.data.length} student records`);
    } catch (error) {
      console.error('Error loading Excel data:', error);
      throw new Error('Failed to load student data');
    }
  }

  public searchBySeatingNumber(seatingNo: number): StudentResult | null {
    return this.data.find(student => student.seating_no === seatingNo) || null;
  }

  public searchByName(name: string): StudentResult[] {
    const searchTerm = name.toLowerCase().trim();
    return this.data.filter(student => 
      student.arabic_name.toLowerCase().includes(searchTerm)
    );
  }

  public getTopStudents(limit: number = 10): StudentResult[] {
    return [...this.data]
      .sort((a, b) => b.total_degree - a.total_degree)
      .slice(0, limit);
  }

  public getStatistics() {
    if (this.data.length === 0) return null;

    const degrees = this.data.map(s => s.total_degree);
    const total = degrees.reduce((sum, degree) => sum + degree, 0);
    const average = total / degrees.length;
    const max = Math.max(...degrees);
    const min = Math.min(...degrees);

    // Grade distribution
    const gradeRanges = {
      excellent: degrees.filter(d => d >= 85).length, // امتياز
      veryGood: degrees.filter(d => d >= 75 && d < 85).length, // جيد جداً
      good: degrees.filter(d => d >= 65 && d < 75).length, // جيد
      acceptable: degrees.filter(d => d >= 50 && d < 65).length, // مقبول
      weak: degrees.filter(d => d < 50).length // ضعيف
    };

    return {
      totalStudents: this.data.length,
      average: Math.round(average * 100) / 100,
      highest: max,
      lowest: min,
      gradeDistribution: gradeRanges
    };
  }

  public getAllData(): StudentResult[] {
    return [...this.data];
  }

  public isDataLoaded(): boolean {
    return this.isLoaded;
  }
}

// Server-side Excel reader for API routes with caching
export class ServerExcelReader {
  private static cache: StudentResult[] | null = null;
  private static lastModified: number = 0;

  public static async readExcelFile(): Promise<StudentResult[]> {
    try {

      const fullPath = path.join(process.cwd(), 'public', 'results.xlsx');

      // Check if file has been modified since last read
      const stats = fs.statSync(fullPath);
      const fileModified = stats.mtime.getTime();

      if (this.cache && fileModified === this.lastModified) {
        return this.cache;
      }

      console.log('Reading Excel file from disk...');
      const buffer = fs.readFileSync(fullPath);

      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) {
        throw new Error('No sheets found in Excel file');
      }
      const worksheet = workbook.Sheets[sheetName];
      if (!worksheet) {
        throw new Error(`Sheet "${sheetName}" not found`);
      }

      const jsonData = XLSX.utils.sheet_to_json(worksheet) as StudentResult[];

      // Cache the data
      this.cache = jsonData;
      this.lastModified = fileModified;

      console.log(`Cached ${jsonData.length} student records`);
      return jsonData;
    } catch (error) {
      console.error('Error reading Excel file:', error);
      throw new Error('Failed to read Excel file');
    }
  }
}
