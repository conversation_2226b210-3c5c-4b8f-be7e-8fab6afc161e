@echo off
REM ========================================================================
REM Thanaweya Portal - Production Startup Script
REM نظام نتائج الثانوية العامة 2025 - سكريبت الإنتاج
REM ========================================================================
REM This script sets up and runs the Thanaweya Portal in production mode
REM يقوم هذا السكريبت بإعداد وتشغيل بوابة الثانوية العامة في وضع الإنتاج
REM ========================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Thanaweya Portal - Production Mode - وضع الإنتاج
color 0E

echo.
echo ========================================================================
echo  Thanaweya Portal - Production Startup
echo  بوابة الثانوية العامة - بدء الإنتاج
echo ========================================================================
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo [ERROR] package.json not found! Make sure you're in the project directory.
    echo [خطأ] لم يتم العثور على package.json! تأكد من أنك في مجلد المشروع.
    pause
    exit /b 1
)

REM Check Node.js installation and version
echo [INFO] Checking Node.js installation...
echo [معلومات] فحص تثبيت Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH!
    echo [خطأ] Node.js غير مثبت أو غير موجود في PATH!
    echo Please install Node.js 18+ from: https://nodejs.org/
    echo يرجى تثبيت Node.js 18+ من: https://nodejs.org/
    pause
    exit /b 1
)

REM Get and validate Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo [SUCCESS] Node.js version: %NODE_VERSION%
echo [نجح] إصدار Node.js: %NODE_VERSION%

REM Check npm installation
echo [INFO] Checking npm installation...
echo [معلومات] فحص تثبيت npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed!
    echo [خطأ] npm غير مثبت!
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo [SUCCESS] npm version: %NPM_VERSION%
echo [نجح] إصدار npm: %NPM_VERSION%

REM Set production environment variables
echo.
echo [INFO] Setting up production environment...
echo [معلومات] إعداد بيئة الإنتاج...
set NODE_ENV=production
set NEXT_TELEMETRY_DISABLED=1
set PORT=3000

REM Create production .env file
echo [INFO] Creating production environment file...
echo [معلومات] إنشاء ملف بيئة الإنتاج...
(
    echo # Production environment variables for Thanaweya Portal
    echo # متغيرات بيئة الإنتاج لبوابة الثانوية العامة
    echo NODE_ENV=production
    echo NEXT_TELEMETRY_DISABLED=1
    echo NEXT_PUBLIC_APP_URL=http://localhost:3000
    echo NEXT_PUBLIC_API_BASE_URL=/api
    echo PORT=3000
    echo COMPRESSION_ENABLED=true
    echo IMAGE_OPTIMIZATION=true
    echo CSP_ENABLED=true
    echo RATE_LIMITING_ENABLED=true
) > .env.production.local

REM Install production dependencies
echo.
echo [INFO] Installing production dependencies...
echo [معلومات] تثبيت تبعيات الإنتاج...
npm ci --only=production
if errorlevel 1 (
    echo [ERROR] Failed to install production dependencies!
    echo [خطأ] فشل في تثبيت تبعيات الإنتاج!
    echo Trying full install...
    echo المحاولة مع التثبيت الكامل...
    npm ci
    if errorlevel 1 (
        echo [ERROR] npm ci failed!
        echo [خطأ] npm ci فشل!
        pause
        exit /b 1
    )
)
echo [SUCCESS] Dependencies installed successfully!
echo [نجح] تم تثبيت التبعيات بنجاح!

REM Check required data files
echo.
echo [INFO] Checking required files...
echo [معلومات] فحص الملفات المطلوبة...
if not exist "public\results.xlsx" (
    echo [ERROR] Critical file missing: public\results.xlsx
    echo [خطأ] ملف مهم مفقود: public\results.xlsx
    echo This file is required for the application to function properly.
    echo هذا الملف مطلوب لعمل التطبيق بشكل صحيح.
    pause
    exit /b 1
) else (
    echo [SUCCESS] Required data file found: public\results.xlsx
    echo [نجح] تم العثور على ملف البيانات المطلوب: public\results.xlsx
)

REM Clean previous builds
echo.
echo [INFO] Cleaning previous builds...
echo [معلومات] تنظيف البناءات السابقة...
if exist ".next" (
    rmdir /s /q ".next" 2>nul
    echo [SUCCESS] Cleaned .next directory
    echo [نجح] تم تنظيف مجلد .next
)
if exist "out" (
    rmdir /s /q "out" 2>nul
    echo [SUCCESS] Cleaned out directory
    echo [نجح] تم تنظيف مجلد out
)

REM Build the application
echo.
echo [INFO] Building application for production...
echo [معلومات] بناء التطبيق للإنتاج...
echo This may take several minutes...
echo قد يستغرق هذا عدة دقائق...
echo.
npm run build
if errorlevel 1 (
    echo [ERROR] Build failed!
    echo [خطأ] فشل البناء!
    echo Check the error messages above for details.
    echo تحقق من رسائل الخطأ أعلاه للتفاصيل.
    pause
    exit /b 1
)
echo [SUCCESS] Build completed successfully!
echo [نجح] تم البناء بنجاح!

REM Verify build output
echo.
echo [INFO] Verifying build output...
echo [معلومات] التحقق من مخرجات البناء...
if not exist ".next" (
    echo [ERROR] Build output not found!
    echo [خطأ] لم يتم العثور على مخرجات البناء!
    pause
    exit /b 1
)
echo [SUCCESS] Build output verified!
echo [نجح] تم التحقق من مخرجات البناء!

REM Start the production server
echo.
echo ========================================================================
echo  Starting Thanaweya Portal Production Server
echo  بدء خادم الإنتاج لبوابة الثانوية العامة
echo ========================================================================
echo.
echo [INFO] Production server configuration:
echo [معلومات] إعدادات خادم الإنتاج:
echo   Environment: Production
echo   البيئة: الإنتاج
echo   Port: %PORT%
echo   المنفذ: %PORT%
echo   URL: http://localhost:%PORT%
echo   الرابط: http://localhost:%PORT%
echo.
echo [INFO] Server features enabled:
echo [معلومات] ميزات الخادم المفعلة:
echo   - Compression: Enabled
echo   - الضغط: مفعل
echo   - Image Optimization: Enabled
echo   - تحسين الصور: مفعل
echo   - Security Headers: Enabled
echo   - رؤوس الأمان: مفعلة
echo   - Rate Limiting: Enabled
echo   - تحديد المعدل: مفعل
echo.
echo [INFO] Press Ctrl+C to stop the server
echo [معلومات] اضغط Ctrl+C لإيقاف الخادم
echo.
echo [INFO] Starting production server...
echo [معلومات] بدء خادم الإنتاج...
echo.

REM Start the production server
npm run start

REM If we reach here, the server was stopped
echo.
echo [INFO] Production server stopped.
echo [معلومات] تم إيقاف خادم الإنتاج.
echo.
echo [INFO] Server shutdown completed.
echo [معلومات] تم إكمال إغلاق الخادم.
echo Thank you for using Thanaweya Portal!
echo شكراً لاستخدام بوابة الثانوية العامة!
pause
