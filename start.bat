@echo off
REM ========================================================================
REM Thanaweya Portal - Automated Startup Script
REM نظام نتائج الثانوية العامة 2025 - سكريبت التشغيل التلقائي
REM ========================================================================
REM This script automatically sets up and runs the Thanaweya Portal application
REM يقوم هذا السكريبت بإعداد وتشغيل تطبيق بوابة الثانوية العامة تلقائياً
REM ========================================================================

setlocal enabledelayedexpansion

REM Set console colors and title
title Thanaweya Portal - نظام نتائج الثانوية العامة 2025
color 0A

echo.
echo ========================================================================
echo  Thanaweya Portal Startup Script
echo  نظام نتائج الثانوية العامة 2025 - سكريبت البدء
echo ========================================================================
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo [ERROR] package.json not found! Make sure you're in the project directory.
    echo [خطأ] لم يتم العثور على package.json! تأكد من أنك في مجلد المشروع.
    pause
    exit /b 1
)

REM Check Node.js installation
echo [INFO] Checking Node.js installation...
echo [معلومات] فحص تثبيت Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH!
    echo [خطأ] Node.js غير مثبت أو غير موجود في PATH!
    echo Please install Node.js 18+ from: https://nodejs.org/
    echo يرجى تثبيت Node.js 18+ من: https://nodejs.org/
    pause
    exit /b 1
)

REM Get Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo [SUCCESS] Node.js version: %NODE_VERSION%
echo [نجح] إصدار Node.js: %NODE_VERSION%

REM Check npm installation
echo [INFO] Checking npm installation...
echo [معلومات] فحص تثبيت npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed or not in PATH!
    echo [خطأ] npm غير مثبت أو غير موجود في PATH!
    pause
    exit /b 1
)

REM Get npm version
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo [SUCCESS] npm version: %NPM_VERSION%
echo [نجح] إصدار npm: %NPM_VERSION%

REM Check if node_modules exists
echo.
echo [INFO] Checking dependencies...
echo [معلومات] فحص التبعيات...
if not exist "node_modules" (
    echo [WARNING] node_modules not found. Installing dependencies...
    echo [تحذير] node_modules غير موجود. جاري تثبيت التبعيات...
    goto :install_deps
) else (
    echo [INFO] node_modules found. Checking if update needed...
    echo [معلومات] تم العثور على node_modules. فحص الحاجة للتحديث...
    
    REM Check if package-lock.json is newer than node_modules
    for %%i in (package-lock.json) do set LOCK_DATE=%%~ti
    for %%i in (node_modules) do set MODULES_DATE=%%~ti
    
    REM Simple check - if package-lock.json exists and is newer, reinstall
    if exist "package-lock.json" (
        echo [INFO] Verifying dependencies are up to date...
        echo [معلومات] التحقق من أن التبعيات محدثة...
        npm ci --silent >nul 2>&1
        if errorlevel 1 (
            echo [WARNING] Dependencies may be outdated. Reinstalling...
            echo [تحذير] قد تكون التبعيات قديمة. إعادة التثبيت...
            goto :install_deps
        )
    )
)
goto :check_data

:install_deps
echo.
echo [INFO] Installing dependencies... This may take a few minutes.
echo [معلومات] تثبيت التبعيات... قد يستغرق هذا بضع دقائق.
echo.
npm ci
if errorlevel 1 (
    echo [ERROR] Failed to install dependencies!
    echo [خطأ] فشل في تثبيت التبعيات!
    echo Trying with npm install...
    echo المحاولة مع npm install...
    npm install
    if errorlevel 1 (
        echo [ERROR] npm install also failed!
        echo [خطأ] npm install فشل أيضاً!
        pause
        exit /b 1
    )
)
echo [SUCCESS] Dependencies installed successfully!
echo [نجح] تم تثبيت التبعيات بنجاح!

:check_data
echo.
echo [INFO] Checking data files...
echo [معلومات] فحص ملفات البيانات...
if not exist "public\results.xlsx" (
    echo [WARNING] results.xlsx not found in public folder!
    echo [تحذير] لم يتم العثور على results.xlsx في مجلد public!
    echo The application will work but search functionality may be limited.
    echo سيعمل التطبيق ولكن وظيفة البحث قد تكون محدودة.
    echo Please ensure results.xlsx is placed in the public folder.
    echo يرجى التأكد من وضع results.xlsx في مجلد public.
    echo.
    set /p CONTINUE="Continue anyway? (y/n) / المتابعة على أي حال؟ (y/n): "
    if /i "!CONTINUE!" neq "y" (
        echo Startup cancelled by user.
        echo تم إلغاء البدء من قبل المستخدم.
        pause
        exit /b 0
    )
) else (
    echo [SUCCESS] Data file found: public\results.xlsx
    echo [نجح] تم العثور على ملف البيانات: public\results.xlsx
)

REM Set up environment variables
echo.
echo [INFO] Setting up environment variables...
echo [معلومات] إعداد متغيرات البيئة...
set NODE_ENV=development
set NEXT_TELEMETRY_DISABLED=1
set PORT=3000

REM Create .env.local if it doesn't exist
if not exist ".env.local" (
    echo [INFO] Creating .env.local file...
    echo [معلومات] إنشاء ملف .env.local...
    (
        echo # Local environment variables for Thanaweya Portal
        echo # متغيرات البيئة المحلية لبوابة الثانوية العامة
        echo NODE_ENV=development
        echo NEXT_TELEMETRY_DISABLED=1
        echo NEXT_PUBLIC_APP_URL=http://localhost:3000
        echo NEXT_PUBLIC_API_BASE_URL=/api
        echo PORT=3000
    ) > .env.local
    echo [SUCCESS] .env.local created successfully!
    echo [نجح] تم إنشاء .env.local بنجاح!
)

REM Check TypeScript compilation
echo.
echo [INFO] Checking TypeScript compilation...
echo [معلومات] فحص تجميع TypeScript...
npm run type-check >nul 2>&1
if errorlevel 1 (
    echo [WARNING] TypeScript compilation has errors!
    echo [تحذير] تجميع TypeScript به أخطاء!
    echo The application may still run, but there might be issues.
    echo قد يعمل التطبيق ولكن قد تكون هناك مشاكل.
    set /p CONTINUE="Continue anyway? (y/n) / المتابعة على أي حال؟ (y/n): "
    if /i "!CONTINUE!" neq "y" (
        echo Startup cancelled by user.
        echo تم إلغاء البدء من قبل المستخدم.
        pause
        exit /b 0
    )
) else (
    echo [SUCCESS] TypeScript compilation successful!
    echo [نجح] تجميع TypeScript نجح!
)

REM Clean previous builds if they exist
echo.
echo [INFO] Cleaning previous builds...
echo [معلومات] تنظيف البناءات السابقة...
if exist ".next" (
    rmdir /s /q ".next" 2>nul
)
if exist "out" (
    rmdir /s /q "out" 2>nul
)

echo.
echo ========================================================================
echo  Starting Thanaweya Portal Development Server
echo  بدء خادم التطوير لبوابة الثانوية العامة
echo ========================================================================
echo.
echo [INFO] The application will be available at:
echo [معلومات] سيكون التطبيق متاحاً على:
echo   Local:   http://localhost:3000
echo   Network: http://[your-ip]:3000
echo.
echo [INFO] Press Ctrl+C to stop the server
echo [معلومات] اضغط Ctrl+C لإيقاف الخادم
echo.
echo [INFO] Starting server...
echo [معلومات] بدء الخادم...
echo.

REM Start the development server
npm run dev

REM If we reach here, the server was stopped
echo.
echo [INFO] Development server stopped.
echo [معلومات] تم إيقاف خادم التطوير.
echo Thank you for using Thanaweya Portal!
echo شكراً لاستخدام بوابة الثانوية العامة!
pause
