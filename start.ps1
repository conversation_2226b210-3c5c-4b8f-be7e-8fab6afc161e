# ========================================================================
# Thanaweya Portal - Automated Startup Script (PowerShell)
# نظام نتائج الثانوية العامة 2025 - سكريبت التشغيل التلقائي
# ========================================================================
# This script automatically sets up and runs the Thanaweya Portal application
# يقوم هذا السكريبت بإعداد وتشغيل تطبيق بوابة الثانوية العامة تلقائياً
# ========================================================================

param(
    [switch]$Production,
    [switch]$Build,
    [switch]$Clean,
    [int]$Port = 3000
)

# Set console title and colors
$Host.UI.RawUI.WindowTitle = "Thanaweya Portal - نظام نتائج الثانوية العامة 2025"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Type = "INFO"
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Type) {
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Cyan }
        default   { Write-Host "[$timestamp] $Message" -ForegroundColor $Color }
    }
}

# Function to check command availability
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to get version of a command
function Get-CommandVersion {
    param([string]$Command)
    try {
        $version = & $Command --version 2>$null
        return $version
    }
    catch {
        return "Unknown"
    }
}

# Main startup function
function Start-ThanaweyaPortal {
    Write-Host ""
    Write-Host "========================================================================" -ForegroundColor Magenta
    Write-Host " Thanaweya Portal Startup Script" -ForegroundColor Magenta
    Write-Host " نظام نتائج الثانوية العامة 2025 - سكريبت البدء" -ForegroundColor Magenta
    Write-Host "========================================================================" -ForegroundColor Magenta
    Write-Host ""

    # Check if we're in the correct directory
    if (-not (Test-Path "package.json")) {
        Write-ColorOutput "package.json not found! Make sure you're in the project directory." "Red" "ERROR"
        Write-ColorOutput "لم يتم العثور على package.json! تأكد من أنك في مجلد المشروع." "Red" "ERROR"
        Read-Host "Press Enter to exit"
        exit 1
    }

    # Check Node.js installation
    Write-ColorOutput "Checking Node.js installation..." "Cyan" "INFO"
    Write-ColorOutput "فحص تثبيت Node.js..." "Cyan" "INFO"
    
    if (-not (Test-Command "node")) {
        Write-ColorOutput "Node.js is not installed or not in PATH!" "Red" "ERROR"
        Write-ColorOutput "Node.js غير مثبت أو غير موجود في PATH!" "Red" "ERROR"
        Write-ColorOutput "Please install Node.js 18+ from: https://nodejs.org/" "Yellow" "WARNING"
        Write-ColorOutput "يرجى تثبيت Node.js 18+ من: https://nodejs.org/" "Yellow" "WARNING"
        Read-Host "Press Enter to exit"
        exit 1
    }

    $nodeVersion = Get-CommandVersion "node"
    Write-ColorOutput "Node.js version: $nodeVersion" "Green" "SUCCESS"
    Write-ColorOutput "إصدار Node.js: $nodeVersion" "Green" "SUCCESS"

    # Check npm installation
    Write-ColorOutput "Checking npm installation..." "Cyan" "INFO"
    Write-ColorOutput "فحص تثبيت npm..." "Cyan" "INFO"
    
    if (-not (Test-Command "npm")) {
        Write-ColorOutput "npm is not installed or not in PATH!" "Red" "ERROR"
        Write-ColorOutput "npm غير مثبت أو غير موجود في PATH!" "Red" "ERROR"
        Read-Host "Press Enter to exit"
        exit 1
    }

    $npmVersion = Get-CommandVersion "npm"
    Write-ColorOutput "npm version: $npmVersion" "Green" "SUCCESS"
    Write-ColorOutput "إصدار npm: $npmVersion" "Green" "SUCCESS"

    # Clean previous builds if requested
    if ($Clean) {
        Write-ColorOutput "Cleaning previous builds..." "Cyan" "INFO"
        Write-ColorOutput "تنظيف البناءات السابقة..." "Cyan" "INFO"
        
        @(".next", "out", "dist") | ForEach-Object {
            if (Test-Path $_) {
                Remove-Item $_ -Recurse -Force -ErrorAction SilentlyContinue
                Write-ColorOutput "Removed $_" "Green" "SUCCESS"
            }
        }
    }

    # Check dependencies
    Write-Host ""
    Write-ColorOutput "Checking dependencies..." "Cyan" "INFO"
    Write-ColorOutput "فحص التبعيات..." "Cyan" "INFO"
    
    if (-not (Test-Path "node_modules") -or $Clean) {
        Write-ColorOutput "Installing dependencies... This may take a few minutes." "Yellow" "WARNING"
        Write-ColorOutput "تثبيت التبعيات... قد يستغرق هذا بضع دقائق." "Yellow" "WARNING"
        
        try {
            Write-Host ""
            npm ci
            if ($LASTEXITCODE -ne 0) {
                Write-ColorOutput "npm ci failed, trying npm install..." "Yellow" "WARNING"
                npm install
                if ($LASTEXITCODE -ne 0) {
                    throw "npm install failed"
                }
            }
            Write-ColorOutput "Dependencies installed successfully!" "Green" "SUCCESS"
            Write-ColorOutput "تم تثبيت التبعيات بنجاح!" "Green" "SUCCESS"
        }
        catch {
            Write-ColorOutput "Failed to install dependencies: $_" "Red" "ERROR"
            Write-ColorOutput "فشل في تثبيت التبعيات: $_" "Red" "ERROR"
            Read-Host "Press Enter to exit"
            exit 1
        }
    } else {
        Write-ColorOutput "Dependencies found. Verifying..." "Cyan" "INFO"
        Write-ColorOutput "تم العثور على التبعيات. التحقق..." "Cyan" "INFO"
        
        # Quick verification
        try {
            npm ci --silent 2>$null
            if ($LASTEXITCODE -ne 0) {
                Write-ColorOutput "Dependencies may be outdated. Consider running with -Clean flag." "Yellow" "WARNING"
                Write-ColorOutput "قد تكون التبعيات قديمة. فكر في التشغيل مع علامة -Clean." "Yellow" "WARNING"
            }
        }
        catch {
            Write-ColorOutput "Dependency verification failed, but continuing..." "Yellow" "WARNING"
        }
    }

    # Check data files
    Write-Host ""
    Write-ColorOutput "Checking data files..." "Cyan" "INFO"
    Write-ColorOutput "فحص ملفات البيانات..." "Cyan" "INFO"
    
    if (-not (Test-Path "public\results.xlsx")) {
        Write-ColorOutput "results.xlsx not found in public folder!" "Yellow" "WARNING"
        Write-ColorOutput "لم يتم العثور على results.xlsx في مجلد public!" "Yellow" "WARNING"
        Write-ColorOutput "The application will work but search functionality may be limited." "Yellow" "WARNING"
        Write-ColorOutput "سيعمل التطبيق ولكن وظيفة البحث قد تكون محدودة." "Yellow" "WARNING"
        Write-ColorOutput "Please ensure results.xlsx is placed in the public folder." "Yellow" "WARNING"
        Write-ColorOutput "يرجى التأكد من وضع results.xlsx في مجلد public." "Yellow" "WARNING"
        
        $continue = Read-Host "Continue anyway? (y/n) / المتابعة على أي حال؟ (y/n)"
        if ($continue -ne "y" -and $continue -ne "Y") {
            Write-ColorOutput "Startup cancelled by user." "Yellow" "WARNING"
            Write-ColorOutput "تم إلغاء البدء من قبل المستخدم." "Yellow" "WARNING"
            exit 0
        }
    } else {
        Write-ColorOutput "Data file found: public\results.xlsx" "Green" "SUCCESS"
        Write-ColorOutput "تم العثور على ملف البيانات: public\results.xlsx" "Green" "SUCCESS"
    }

    # Set up environment variables
    Write-Host ""
    Write-ColorOutput "Setting up environment variables..." "Cyan" "INFO"
    Write-ColorOutput "إعداد متغيرات البيئة..." "Cyan" "INFO"
    
    $env:NEXT_TELEMETRY_DISABLED = "1"
    $env:PORT = $Port.ToString()
    
    if ($Production) {
        $env:NODE_ENV = "production"
    } else {
        $env:NODE_ENV = "development"
    }

    # Create .env.local if it doesn't exist
    if (-not (Test-Path ".env.local")) {
        Write-ColorOutput "Creating .env.local file..." "Cyan" "INFO"
        Write-ColorOutput "إنشاء ملف .env.local..." "Cyan" "INFO"
        
        $envContent = @"
# Local environment variables for Thanaweya Portal
# متغيرات البيئة المحلية لبوابة الثانوية العامة
NODE_ENV=$($env:NODE_ENV)
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_APP_URL=http://localhost:$Port
NEXT_PUBLIC_API_BASE_URL=/api
PORT=$Port
"@
        $envContent | Out-File -FilePath ".env.local" -Encoding UTF8
        Write-ColorOutput ".env.local created successfully!" "Green" "SUCCESS"
        Write-ColorOutput "تم إنشاء .env.local بنجاح!" "Green" "SUCCESS"
    }

    # TypeScript check
    Write-Host ""
    Write-ColorOutput "Checking TypeScript compilation..." "Cyan" "INFO"
    Write-ColorOutput "فحص تجميع TypeScript..." "Cyan" "INFO"
    
    try {
        npm run type-check 2>$null | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "TypeScript compilation has errors!" "Yellow" "WARNING"
            Write-ColorOutput "تجميع TypeScript به أخطاء!" "Yellow" "WARNING"
            Write-ColorOutput "The application may still run, but there might be issues." "Yellow" "WARNING"
            Write-ColorOutput "قد يعمل التطبيق ولكن قد تكون هناك مشاكل." "Yellow" "WARNING"
            
            $continue = Read-Host "Continue anyway? (y/n) / المتابعة على أي حال؟ (y/n)"
            if ($continue -ne "y" -and $continue -ne "Y") {
                Write-ColorOutput "Startup cancelled by user." "Yellow" "WARNING"
                Write-ColorOutput "تم إلغاء البدء من قبل المستخدم." "Yellow" "WARNING"
                exit 0
            }
        } else {
            Write-ColorOutput "TypeScript compilation successful!" "Green" "SUCCESS"
            Write-ColorOutput "تجميع TypeScript نجح!" "Green" "SUCCESS"
        }
    }
    catch {
        Write-ColorOutput "TypeScript check failed, but continuing..." "Yellow" "WARNING"
    }

    # Build if requested
    if ($Build -or $Production) {
        Write-Host ""
        Write-ColorOutput "Building application..." "Cyan" "INFO"
        Write-ColorOutput "بناء التطبيق..." "Cyan" "INFO"
        
        try {
            npm run build
            if ($LASTEXITCODE -ne 0) {
                throw "Build failed"
            }
            Write-ColorOutput "Build completed successfully!" "Green" "SUCCESS"
            Write-ColorOutput "تم البناء بنجاح!" "Green" "SUCCESS"
        }
        catch {
            Write-ColorOutput "Build failed: $_" "Red" "ERROR"
            Write-ColorOutput "فشل البناء: $_" "Red" "ERROR"
            Read-Host "Press Enter to exit"
            exit 1
        }
    }

    # Start the server
    Write-Host ""
    Write-Host "========================================================================" -ForegroundColor Magenta
    Write-Host " Starting Thanaweya Portal Server" -ForegroundColor Magenta
    Write-Host " بدء خادم بوابة الثانوية العامة" -ForegroundColor Magenta
    Write-Host "========================================================================" -ForegroundColor Magenta
    Write-Host ""
    
    Write-ColorOutput "The application will be available at:" "Cyan" "INFO"
    Write-ColorOutput "سيكون التطبيق متاحاً على:" "Cyan" "INFO"
    Write-Host "  Local:   http://localhost:$Port" -ForegroundColor Green
    Write-Host "  Network: http://[your-ip]:$Port" -ForegroundColor Green
    Write-Host ""
    Write-ColorOutput "Press Ctrl+C to stop the server" "Yellow" "INFO"
    Write-ColorOutput "اضغط Ctrl+C لإيقاف الخادم" "Yellow" "INFO"
    Write-Host ""
    Write-ColorOutput "Starting server..." "Cyan" "INFO"
    Write-ColorOutput "بدء الخادم..." "Cyan" "INFO"
    Write-Host ""

    try {
        if ($Production) {
            npm run start
        } else {
            npm run dev
        }
    }
    catch {
        Write-ColorOutput "Server stopped or failed to start: $_" "Yellow" "WARNING"
    }
    finally {
        Write-Host ""
        Write-ColorOutput "Development server stopped." "Cyan" "INFO"
        Write-ColorOutput "تم إيقاف خادم التطوير." "Cyan" "INFO"
        Write-ColorOutput "Thank you for using Thanaweya Portal!" "Green" "SUCCESS"
        Write-ColorOutput "شكراً لاستخدام بوابة الثانوية العامة!" "Green" "SUCCESS"
    }
}

# Handle script execution
try {
    Start-ThanaweyaPortal
}
catch {
    Write-ColorOutput "An unexpected error occurred: $_" "Red" "ERROR"
    Write-ColorOutput "حدث خطأ غير متوقع: $_" "Red" "ERROR"
    Read-Host "Press Enter to exit"
    exit 1
}
