{"version": 2, "name": "thanaweya-portal", "alias": ["thanaweya-results", "نتائج-الثانوية"], "regions": ["cle1", "iad1"], "build": {"env": {"NODE_ENV": "production"}}, "env": {"NODE_ENV": "production", "NEXT_PUBLIC_APP_URL": "@app_url", "NEXT_PUBLIC_API_BASE_URL": "@api_base_url"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/results", "destination": "/", "permanent": true}, {"source": "/search", "destination": "/", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}], "trailingSlash": false, "cleanUrls": true, "github": {"enabled": false}}